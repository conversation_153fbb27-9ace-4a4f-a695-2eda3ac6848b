# 💬 Temporary Chat Guide

Temporary Chat allows you to interact with the assistant in a lightweight, popup-style chat — without creating a new thread or saving any messages. It's perfect for side questions, quick tests, or one-off tasks.

![temporarily](https://github.com/user-attachments/assets/e0c9874c-e06a-4d2b-a630-1871c6fe3a69)


## 🔄 How It Works

* Open or close the temporary chat anytime using the shortcut (`⌘K`) or the button in the top right corner.
* You can reset the temporary chat with a shortcut (`⌘E`).
* It appears as a **right-side sliding panel** so you can keep your main chat open.
* Messages sent here are **not saved** to history — once closed, they're gone.
* Temporary chat supports its own **System Prompt**, allowing you to customize behavior.

<img width="1418" alt="systemprompt" src="https://github.com/user-attachments/assets/8e851ace-9deb-4cd4-87ef-2ab56ecad731" />


## 🧠 Why It's Useful

Imagine you're in the middle of an important discussion and you suddenly need a quick translation or to test a tool — but don't want to clutter the current chat. Temporary Chat is built exactly for that.

**Use cases include:**

* Asking a quick side question without breaking the main conversation flow
* Testing a prompt or tool before using it in your main chat
* Setting up a dedicated system prompt for focused tasks (e.g. translation, formatting)

## 📝 Real-World Example

For example, if you are not fluent in English, you can set a translation-focused system prompt in the temporary chat (e.g. "Translate everything naturally between Korean and English without extra explanation"). While having a main conversation, whenever you need a translation, you can quickly open the temporary chat using the shortcut, get the translation you need to understand the context, and then return to your main chat to continue the discussion.

---

Temporary Chat helps keep your main conversations clean, while still giving you flexibility and speed for all your side tasks.
