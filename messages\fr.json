{"Common": {"cancel": "Annuler", "update": "Mettre à jour", "continue": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "back": "Retour", "next": "Suivant", "create": "<PERSON><PERSON><PERSON>", "showLess": "Affiche<PERSON> moins", "showMore": "Afficher plus", "generate": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "editAgent": "Modifier l'Agent", "search": "Rechercher...", "approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "saving": "Enregistrement...", "optional": "Facultatif", "deleting": "Suppression...", "run": "Exécuter", "description": "Description", "defaultValue": "Valeur par Défaut", "empty": "Vide", "required": "Requis", "options": "Options", "status": "Statut", "result": "Résultat", "startedAt": "Comme<PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "addOption": "Ajouter Option", "tool": "Outil", "selectTool": "Sélectionner Outil...", "noResults": "Aucun résultat.", "generateInputWithAI": "Générer Entrée avec IA", "generatingInputWithAI": "Génération d'entrée avec IA...", "inputGeneratedSuccessfully": "En<PERSON><PERSON> générée avec succès", "failedToGenerateInput": "Échec de génération d'entrée", "createWithExample": "Créer avec Exemple", "resultsFound": "{count} résultats trouvés", "youAreAnExpertIn": "Vous êtes un expert en {role}", "sharedBy": "Partagé par {userName}"}, "Auth": {"SignIn": {"title": "Bon retour", "description": "Connectez-vous pour continuer vers votre compte", "oauthClientIdNotSet": "L'ID client {provider} n'est pas configuré", "noAccount": "Vous n'avez pas de compte ?", "signUp": "S'inscrire", "signIn": "Se connecter", "orContinueWith": "OU CONTINUER AVEC"}, "SignUp": {"title": "<PERSON><PERSON><PERSON> un compte", "signIn": "Se connecter", "description": "Inscrivez-vous à votre compte", "step1": "Commencez votre parcours avec nous en saisissant votre adresse e-mail", "step2": "J'utiliserai ce nom quand nous discuterons", "step3": "Créez un mot de passe fort pour sécuriser votre compte", "signUp": "S'inscrire", "invalidEmail": "Adresse e-mail invalide", "emailAlreadyExists": "L'e-mail existe déjà", "nameRequired": "Le nom est requis", "passwordRequired": "Le mot de passe est requis", "createAccount": "<PERSON><PERSON><PERSON> un compte"}, "Intro": {"description": "Bienvenue sur better-chatbot. Connectez-vous pour découvrir nos outils de conversation alimentés par l'IA."}}, "Chat": {"Error": "<PERSON><PERSON><PERSON>", "thisMessageWasNotSavedPleaseTryTheChatAgain": "Ce message n'a pas été enregistré. Veuillez réessayer le chat.", "Greeting": {"goodMorning": "<PERSON><PERSON><PERSON>, {name}", "goodAfternoon": "<PERSON><PERSON><PERSON>, {name}", "goodEvening": "<PERSON><PERSON><PERSON>, {name}", "niceToSeeYouAgain": "<PERSON> de vous revoir, {name}", "whatAreYouWorkingOnToday": "Sur quoi travaillez-vous aujourd'hui, {name} ?", "letMeKnowWhenYoureReadyToBegin": "Faites-moi savoir quand vous êtes prêt à commencer.", "whatAreYourThoughtsToday": "Quelles sont vos pensées aujourd'hui ?", "whereWouldYouLikeToStart": "Par où aimeriez-vous commencer ?", "whatAreYouThinking": "À quoi pensez-vous, {name} ?"}, "TemporaryChat": {"toggleTemporaryChat": "Basculer le Chat Temporaire", "temporaryChat": "Chat Temporaire", "resetChat": "Réinitialiser le Chat", "thisChatWontBeSaved": "Ce chat ne sera pas enregistré.", "feelFreeToAskAnythingTemporarily": "N'hésitez pas à demander quoi que ce soit temporairement", "temporaryChatInstructions": "Instructions du Chat Temporaire", "temporaryChatInstructionsPlaceholder": "Entrez vos instructions ici", "temporaryChatInstructionsDescription": "Vous pouvez définir des instructions pour le chat temporaire. Cela sera utilisé comme prompt système pour le chat temporaire."}, "placeholder": "Demandez n'importe quoi ou @mentionnez", "Tool": {"webSearching": "Recherche sur le web...", "searchedTheWeb": "Recherché sur le web", "toolModeDescription": "Choisissez comment les outils sont utilisés:\n• Auto: L'IA décide quand utiliser les outils\n• <PERSON>: De<PERSON>e permission avant d'utiliser les outils\n• Aucun: Désactive tous les outils", "toolsSetupDescription": "Sélectionnez les outils que le chatbot peut utiliser.\nLe chatbot utilisera les outils sélectionnés selon son propre jugement.\n\nVous pouvez aussi forcer l'utilisation d'outils spécifiques via @mention.", "selectToolMode": "Sélectionner le mode d'outils", "autoToolModeDescription": "<PERSON><PERSON><PERSON>e quand utiliser les outils sans vous demander", "manualToolModeDescription": "Demande votre permission avant d'utiliser des outils", "noneToolModeDescription": "Ne pas utiliser d'outils. @mention est toujours disponible.", "toolsSetup": "Configuration des Outils", "preset": "Préréglage", "toolPresets": "Préréglages d'Outils", "saveAsPreset": "Enregistrer comme Préréglage", "saveAsPresetDescription": "Enregistrer la configuration actuelle des outils comme préréglage.", "noPresetsAvailableYet": "Aucun préréglage disponible pour le moment", "presetNameCannotBeEmpty": "Le nom du préréglage ne peut pas être vide", "presetNameAlreadyExists": "Le nom du préréglage existe déjà", "presetSaved": "Préréglage enregistré", "clickSaveAsPresetToGetStarted": "Cliquez sur Enregistrer comme Préréglage pour commencer.", "searchOptions": "Options de Recherche", "searchOptionsDescription": "Vous pouvez passer des options de recherche au chatbot, comme le nombre maximum de résultats de recherche, la date de recherche, etc.", "defaultToolKit": {"visualization": "Visualisation de données", "webSearch": "Rechercher sur le le Web"}}, "VoiceChat": {"title": "Mode Chat Vocal", "compactDisplayMode": "Mode d'affichage compact", "conversationDisplayMode": "Mode d'affichage de conversation", "pleaseCloseTheVoiceChatAndTryAgain": "Veuillez fermer le chat vocal et réessayer.", "startConversation": "<PERSON><PERSON><PERSON><PERSON> la conversation", "closeMic": "<PERSON><PERSON><PERSON> le <PERSON>", "openMic": "<PERSON><PERSON><PERSON><PERSON><PERSON> le micro", "endConversation": "Te<PERSON><PERSON> la <PERSON>", "toggleVoiceChat": "<PERSON><PERSON><PERSON><PERSON> le Chat Vocal", "readyWhenYouAreJustStartTalking": "<PERSON><PERSON><PERSON><PERSON> quand vous l'êtes—commencez simplement à parler.", "yourMicIsOff": "Votre micro est éteint.", "preparing": "Préparation...", "startVoiceChat": "<PERSON><PERSON><PERSON><PERSON> le chat vocal ?"}, "Thread": {"chat": "Cha<PERSON>", "renameChat": "<PERSON>mmer", "deleteChat": "<PERSON><PERSON><PERSON><PERSON> le Chat", "deleteUnarchivedChats": "Supprimer Tous les Chats Non Archivés", "confirmDeleteUnarchivedChats": "Êtes-vous sûr de vouloir supprimer tous les chats non archivés ?", "thisActionCannotBeUndone": "Cette action ne peut pas être annulée.", "unarchivedChatsDeleted": "Les chats non archivés ont été supprimés", "failedToDeleteUnarchivedChats": "Échec de la suppression des chats non archivés", "failedToDeleteThread": "Échec de suppression du fil", "threadDeleted": "Fil supprimé", "failedToUpdateThread": "Échec de mise à jour du fil", "titleRequired": "Le titre est requis", "threadUpdated": "Fil mis à jour", "areYouSureYouWantToDeleteThisChatThread": "Êtes-vous sûr de vouloir supprimer ce fil de chat ?"}, "ChatPreferences": {"title": "Préférences de Chat", "whatShouldWeCallYou": "Comment devrions-nous vous appeler ?", "botName": "Nom de l'Assistant", "whatBestDescribesYourWork": "Qu'est-ce qui décrit le mieux votre travail ?", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "Quelles préférences personnelles devraient être prises en compte dans les réponses ?", "responseStyleExample1": "ex. gardez les explications brèves et directes", "responseStyleExample2": "ex. quand j'apprends de nouveaux concepts, je trouve les analogies particulièrement utiles", "responseStyleExample3": "ex. posez des questions de clarification avant de donner des réponses détaillées", "responseStyleExample4": "ex. rappelez-vous que je programme principalement en Python (je ne suis pas débutant en programmation)", "professionExample1": "ex. ingénieur logiciel", "professionExample2": "ex. chef de produit", "professionExample3": "ex. responsable marketing", "professionExample4": "ex. directeur des ventes", "professionExample5": "ex. analyste commercial", "preferencesSaved": "Préférences enregistrées", "failedToSavePreferences": "Échec de l'enregistrement des préférences", "userInstructions": "Instructions U<PERSON><PERSON><PERSON><PERSON>", "userInstructionsDescription": "Présentez-vous et obtenez une réponse plus personnalisée.", "mcpInstructions": "Instructions MCP", "mcpInstructionsDescription": "Personnalisez les instructions du serveur MCP."}}, "Layout": {"toggleSidebar": "Basculer la Barre Latérale", "newChat": "Nouveau Chat", "mcpConfiguration": "Configuration MCP", "agents": "Agents", "newAgent": "Nouvel Agent", "createAgent": "<PERSON><PERSON><PERSON> un agent", "createYourOwnAgent": "<PERSON><PERSON>ez votre propre agent IA spécialisé avec des fonctionnalités et une personnalité uniques", "whatIsAgent": "Qu'est-ce qu'un Agent ?", "agentDescription": "Les agents sont des assistants IA spécialisés qui peuvent être personnalisés avec des rôles, des instructions et des outils spécifiques pour vous aider dans diverses tâches.", "specializedAIAssistant": "Assistant I<PERSON> Spécial<PERSON>", "specializedAIAssistantDescription": "Chaque agent peut être personnalisé avec des rôles spécifiques, des personnalités et des domaines d'expertise pour fournir une assistance ciblée selon vos besoins uniques.", "customInstructions": "Instructions Personnalisées", "customInstructionsDescription": "Définissez des prompts système détaillés et des directives comportementales pour façonner la façon dont votre agent répond et interagit avec vous.", "toolIntegration": "Intégration d'Outils", "toolIntegrationDescription": "Connectez vos agents aux serveurs MCP, aux flux de travail et à d'autres outils pour étendre leurs capacités au-delà de la conversation.", "agentExamples": "Exemples d'Agents", "businessAssistant": "Assistant Business", "businessAssistantDescription": "Spécialisé dans l'analyse commerciale, la génération de rapports et la communication professionnelle.", "creativeWriter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creativeWriterDescription": "Axé sur la narration, la création de contenu et le brainstorming créatif.", "technicalExpert": "Expert Technique", "technicalExpertDescription": "Équipé d'outils de développement et d'expertise en codage pour les tâches techniques.", "createFirstAgentToStart": "<PERSON><PERSON>ez votre premier agent pour commencer !", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "lastWeek": "7 derniers jours", "older": "Plus ancien", "recentChats": "<PERSON>ts Récents", "deleteAllChats": "Supp<PERSON><PERSON> <PERSON>us les Chats", "deleteUnarchivedChats": "Supprimer les Chats Non Archivés", "noConversationsYet": "Aucune conversation pour le moment", "deletingAllChats": "Suppression de tous les fils...", "deletingUnarchivedChats": "Suppression des fils non archivés...", "allChatsDeleted": "Tous les fils supprimés", "unarchivedChatsDeleted": "Fils non archivés supprimés", "failedToDeleteAllChats": "Échec de suppression de tous les fils", "failedToDeleteUnarchivedChats": "Échec de suppression des fils non archivés", "chatPreferences": "Préférences de Chat", "keyboardShortcuts": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Thème", "signOut": "Se déconnecter", "language": "<PERSON><PERSON>", "showAllChats": "Voir Tous les Chats", "showLessChats": "Affiche<PERSON> moins", "reportAnIssue": "Signaler un problème", "joinCommunity": "Rejoindre la Communauté", "workflow": "Flux de Travail"}, "Archive": {"title": "Archive", "addArchive": "Ajouter une Archive", "archiveName": "Nom de l'Archive", "archiveDescription": "Description de l'Archive", "archiveDescriptionPlaceholder": "Les archives sont des espaces pour stocker l'historique des chats.", "noArchives": "Aucune archive", "createFirstArchive": "Créez votre première archive", "archiveCreated": "Archive créée", "archiveUpdated": "Archive mise à jour", "archiveDeleted": "Archive supprimée", "failedToCreateArchive": "Échec de la création de l'archive", "failedToUpdateArchive": "Échec de la mise à jour de l'archive", "failedToDeleteArchive": "Échec de la suppression de l'archive", "editArchive": "Modifier l'Archive", "editArchiveDescription": "Modifier les informations de l'archive", "deleteArchive": "Supprimer l'Archive", "confirmDeleteArchive": "Êtes-vous sûr de vouloir supprimer cette archive ?", "deleteArchiveDescription": "Cette archive et tous ses éléments seront définitivement supprimés. Cette action ne peut pas être annulée.", "addToArchive": "Ajouter à l'Archive", "removeFromArchive": "Supprimer de l'Archive", "itemAddedToArchive": "Élément ajouté à l'archive", "itemRemovedFromArchive": "Élément supprimé de l'archive"}, "Agent": {"title": "Agent", "generatingAgent": "Génération de l'Agent...", "agentNameAndIconLabel": "Donnez un nom et une icône à votre agent.", "agentDescriptionLabel": "Ajoutez une brève description de ce que fait cet agent.", "agentDescriptionPlaceholder": "Ceci n'est qu'une description de l'agent, ce n'est pas critique.", "agentSettingsDescription": "À partir d'ici, ce sont des paramètres qui peuvent affecter l'agent.", "thisAgentIs": "Cet agent est un expert en", "expertIn": "", "agentRolePlaceholder": "analyse boursière", "agentInstructionsLabel": "N'hésitez pas à écrire le rôle, la personnalité, les directives, les connaissances, etc. de l'agent.", "agentInstructionsPlaceholder": "Cet agent aide à l'analyse boursière. Il utilise des outils de recherche web pour obtenir des informations boursières...", "agentToolsLabel": "Ajoutez des outils que cet agent peut utiliser.", "loadingTools": "Chargement des outils...", "addTools": "Veuillez ajouter des outils.", "generateAgentGreeting": "Bonjour ! Je vais vous aider à créer votre propre agent. Que souhaitez-vous créer ?", "generateAgentDetailedGreeting": "Bonjour ! Je vais vous aider à créer votre propre agent. Que souhaitez-vous créer ? Vous pouvez écrire brièvement ou en détail.", "inputPromptHere": "saisissez le prompt ici...", "agentNamePlaceholder": "better-agent", "myAgents": "Mes Agents", "sharedAgents": "Agents Partagés", "noAgents": "Pas encore d'agents", "createFirst": "Créez votre premier agent pour commencer", "noSharedAgents": "Aucun agent partagé", "noSharedAgentsDescription": "Aucun agent public n'est disponible pour les marquer", "noDescription": "Aucune description fournie", "bookmarkAdded": "Agent mis en favori", "bookmarkRemoved": "Favori supprimé", "bookmarkedAgent": "Agent en favori", "addBookmark": "Mettre l'agent en favori", "removeBookmark": "Supprimer le favori", "visibilityUpdated": "Visibilité mise à jour", "deleted": "Agent supprimé", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cet agent?", "makePrivate": "<PERSON><PERSON>", "makeReadonly": "Rendre en Lecture Seule", "makePublic": "Rendre Public", "visibility": "Visibilité", "private": "Priv<PERSON>", "readOnly": "Lecture Seule", "public": "Public", "privateDescription": "Seul vous pouvez voir, modifier et utiliser cet agent.", "readOnlyDescription": "Les autres peuvent voir et utiliser comme outil, mais seul vous pouvez modifier.", "publicDescription": "Tout le monde peut voir, modifier et utiliser cet agent comme outil."}, "KeyboardShortcuts": {"title": "<PERSON><PERSON><PERSON><PERSON>", "newChat": "Nouveau Chat", "toggleTemporaryChat": "Basculer <PERSON>", "toggleSidebar": "Basculer <PERSON>", "toolMode": "Mode Outils", "lastMessageCopy": "<PERSON><PERSON><PERSON> le Dernier <PERSON>", "openChatPreferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteThread": "<PERSON><PERSON><PERSON><PERSON>", "openShortcutsPopup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleVoiceChat": "<PERSON><PERSON><PERSON><PERSON>"}, "MCP": {"marketplace": "<PERSON><PERSON>", "addMcpServer": "<PERSON><PERSON><PERSON>", "configureYourMcpServerConnectionSettings": "Configurez les paramètres de connexion de votre serveur MCP", "mcpConfiguration": "Configuration MCP", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "Le nom ne doit contenir que des caractères alphanumériques (A-Z, a-z, 0-9) et des traits d'union (-)", "nameIsRequired": "Le nom est requis", "configurationSavedSuccessfully": "Configuration enregistrée avec succès", "enterMcpServerName": "Entrez le nom du serveur MCP", "saveConfiguration": "Enregistrer la Configuration", "toolsTest": "Test des Outils", "refresh": "Actualiser", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "configuration": "Configuration", "availableTools": "Outils Disponibles", "noToolsAvailable": "Aucun outil disponible", "overviewTitle": "Connectez Votre Premier Serveur", "overviewDescription": "Ajoutez des serveurs MCP pour débloquer de puissantes intégrations IA", "searchTools": "Rechercher des outils", "detail": "Détail", "noSchemaPropertiesAvailable": "Aucune propriété de schéma disponible", "createInputWithAI": "Créer Entrée avec IA", "generateExampleInputJSON": "Générer JSON d'Entrée d'Exemple", "enterPromptToGenerateExampleInputJSON": "Entrez un prompt pour générer un JSON d'entrée d'exemple pour l'outil sélectionné.", "callTool": "<PERSON><PERSON><PERSON> l'outil", "customInstructions": "Instructions personnalisées", "serverCustomInstructionsPlaceholder": "Ces lignes seront ajoutées à l'invite système chaque fois qu'un outil de ce serveur sera disponible.", "nameAlreadyExists": "Le nom existe déjà", "additionalInstructions": "Instructions de Personnalisation d'Outils", "inputSchema": "Schéma d'Entrée", "toolCustomizationInstructions": "Les instructions de personnalisation d'outils seront ajoutées à l'invite système lorsque l'outil sera disponible.\nexemple) Toujours entrer l'email <NAME_EMAIL>.", "mcpServerCustomization": "Personnalisation du Serveur MCP", "mcpServerCustomizationDescription": "Les instructions de personnalisation du serveur MCP seront ajoutées à l'invite système lorsque le serveur MCP sera disponible.", "toolCustomizationInstructionsPlaceholder": "Les instructions de personnalisation d'outils ne sont pas disponibles.", "mcpServerCustomizationPlaceholder": "ex. Si la valeur d'entrée est un email, toujours entrer l'email <NAME_EMAIL>."}, "Error": {}, "Info": {"mcpAddingDisabled": "L'ajout de serveurs MCP a été désactivé par l'administrateur.", "vercelSyncDelay": "Fonctionnement sur Vercel\n\nLes modifications MCP peuvent prendre 10-15 secondes à se synchroniser. Veuillez patienter un moment si les changements n'apparaissent pas immédiatement après avoir ajouté, modifié ou supprimé des serveurs."}, "Workflow": {"title": "Flux de Travail", "createWorkflow": "Créer Flux de Travail", "draft": "Brouillon", "publish": "Publier", "createWorkflowDescription": "Créez des flux de travail comme outils puissants pour votre chatbot.", "workflowDescription": "Ceux-ci peuvent être déclenchés pendant les conversations pour automatiser des tâches complexes.", "nameAndIcon": "Nom et Icône", "workflowNamePlaceholder": "Le chatbot reconnaîtra ceci comme nom d'outil", "description": "Description", "descriptionPlaceholder": "Le chatbot verra ceci comme description d'outil", "inputNodeCannotBeDeleted": "Le nœud d'entrée ne peut pas être supprimé", "autoSaveDescription": "Sauvegarde automatique toutes les 10 secondes", "draftDescription": "Actuellement en Brouillon.\n\nCliquez sur Publier pour le rendre disponible au chatbot\n(mais plus modifiable).", "publishedDescription": "Actuellement Publié et disponible pour le chatbot.\n\nCliquez sur Brouillon pour le rendre modifiable\n(mais non disponible pour le chatbot).", "private": "Priv<PERSON>", "readonly": "Lecture Seule", "public": "Public", "privateDescription": "Seul vous pouvez voir, modifier et utiliser ce flux de travail comme outil.", "readonlyDescription": "Les autres peuvent voir et utiliser comme outil, mais seul vous pouvez modifier.", "publicDescription": "N'importe qui peut voir, modifier et utiliser ce flux de travail comme outil.", "visibilityDescription": "Contrôle qui peut accéder et modifier ce flux de travail", "nodeDescriptionPlaceholder": "description du nœud...", "nextNode": "<PERSON><PERSON><PERSON>", "nextNodeDescription": "Ajouter un nœud suivant à ce flux de travail.", "addNextNode": "<PERSON><PERSON><PERSON>", "inputFields": "Champs d'Entrée", "addInputField": "A<PERSON>ter Champ d'En<PERSON>", "inputFieldsDescription": "Définissez le schéma de paramètres pour ce flux de travail.\n\nQuand le chatbot utilisera ceci comme outil,\nil fournira des valeurs selon ce schéma.", "fieldEditor": "<PERSON><PERSON><PERSON>", "variableName": "Nom de Variable", "variableNamePlaceholder": "Entrez le nom de variable...", "fieldDescriptionPlaceholder": "Entrez la description du champ...", "defaultValuePlaceholder": "Entrez la valeur {type} par défaut...", "selectOptionPlaceholder": "Sélectionner option...", "unlink": "<PERSON><PERSON><PERSON>", "elseIfDescription": "Si la condition n'est pas remplie, la logique à exécuter est définie.", "elseDescription": "Si la condition n'est pas remplie, la logique à exécuter est définie.", "addCondition": "Ajouter Condition", "noVariablesFound": "Aucune variable trouvée", "outputVariables": "Variables de Sortie", "outputVariablesDescription": "Les variables de sortie sont les variables qui sont générées depuis le flux de travail.", "addOutputVariable": "Ajouter Variable de Sortie", "outputSchema": "<PERSON>h<PERSON><PERSON> de Sortie", "addMessage": "Ajouter Message", "messagesDescription": "Générer des données par traitement LLM.\n\nUtilisez '/' pour mentionner et référencer des données de nœuds précédents comme entrée.\n\nAvec Sortie Structurée activée, parfait pour la transformation, le formatage et la validation de données.", "descriptionAndSchema": "Description et Schéma", "noDescriptionAndSchema": "Pas de description et schéma", "toolDescription": "Fournissez les informations nécessaires pour que LLM génère les paramètres d'outil.\n\nUtilisez '/' pour mentionner des données de nœuds précédents.", "generateInputWithAIDescription": "Écrivez un prompt pour générer une entrée pour le flux de travail", "example": {"babyResearch": "Recherche de Bébé", "getWeather": "<PERSON><PERSON><PERSON><PERSON>"}, "selectVariable": "Sélectionner Variable", "structuredOutput": "<PERSON><PERSON><PERSON>", "structuredOutputDescription": "Générer une réponse comme objet JSON avec schéma défini", "outputSchemaEditor": "<PERSON><PERSON><PERSON> <PERSON> Schéma de Sortie", "addField": "<PERSON><PERSON><PERSON>mp", "saveSchema": "Enregis<PERSON><PERSON>", "generateSchemaWithAI": "<PERSON><PERSON><PERSON>rer Schéma avec IA", "describeOutputDataRequest": "Fournissez des données JSON d'exemple qui représentent ce que ce nœud devrait produire\n\nExemple: {eg}", "generatingJsonSchemaWithAI": "Génération du schéma JSON avec IA...", "jsonSchemaGeneratedSuccessfully": "Schéma JSON généré avec succès !", "failedToGenerateSchema": "Échec de génération du schéma", "jsonSchemaEditorDescription": "Édition directe de schéma JSON avec assistance IA. Supporte les structures imbriquées complexes et les tableaux.", "template": "<PERSON><PERSON><PERSON><PERSON>", "templateDescription": "Générer des documents de modèle.\n\nUtilisez '/' pour référencer et utiliser les valeurs de sortie d'autres nœuds.", "kindsDescription": {"input": "Définit les paramètres d'entrée que le chatbot fournira en utilisant ce flux de travail comme outil.\n\nSpécifie la structure des données et les règles de validation pour l'exécution de l'outil.", "output": "Collecte et retourne les résultats finaux de l'exécution de votre flux de travail.\n\nCombine les données de plusieurs nœuds dans la réponse finale de l'outil.", "llm": "Génère du texte ou des données structurées en utilisant des modèles IA.\n\nRéférence les sorties de nœuds précédents avec des mentions '/' pour créer des réponses conscientes du contexte.\n\nUtilisez Sortie Structurée pour transformer, formater et valider les données - pas seulement pour la génération de texte.", "tool": "Exécute des outils MCP ou des services externes.\n\nÉcrivez des instructions dans les messages, et LLM générera les paramètres d'outil requis à partir de votre contexte.", "note": "Ajoute de la documentation et des commentaires pour organiser la logique de votre flux de travail.\n\nAide les membres de l'équipe à comprendre les processus complexes de flux de travail.", "code": "Exécute des scripts de code personnalisé avec accès aux données de nœuds précédents.\n\nExécute JavaScript, Python ou d'autres langages dans votre flux de travail (bientôt disponible).", "http": "Récupère des données d'APIs externes et de services web via des requêtes HTTP.\n\nIntègre avec des APIs REST, webhooks et services tiers.", "template": "Crée des documents dynamiques en combinant du texte avec des données de nœuds précédents.\n\nGénère des emails, rapports ou contenu formaté en utilisant la substitution de variables.", "condition": "Ajoute une logique conditionnelle pour brancher votre flux de travail basé sur l'évaluation des données.\n\nCrée une logique if-else pour gérer différents scénarios et conditions de données."}, "greeting": {"buildAutomationTitle": "Construire l'Automatisation en Connectant les Nœuds", "buildAutomationDescription": "Connectez divers nœuds pour automatiser des tâches complexes. Chaque nœud gère des fonctions spécifiques, et les données circulent séquentiellement pour le traitement.", "chatbotToolTitle": "Utiliser comme <PERSON><PERSON>", "chatbotToolDescription": "Le but principal des flux de travail est de les utiliser comme outils dans les conversations de chatbot. Transformez les tâches répétitives en flux de travail pour une exécution facile pendant les chats.", "parameterBasedTitle": "️ Démarrage Basé sur les Paramètres", "parameterBasedDescription": "Les nœuds d'entrée définissent des structures de paramètres, pas des déclencheurs. Ils spécifient le format de données nécessaire quand le chatbot appelle ce flux de travail comme outil.", "exampleTitle": "Exemple d'Utilisation", "exampleDescription": "<PERSON><PERSON>ez un flux de travail \"<PERSON><PERSON><PERSON><PERSON> → <PERSON><PERSON><PERSON><PERSON> → <PERSON>voy<PERSON>\", puis exécutez-le facilement dans les conversations de chatbot avec \"@email_workflow\".", "availableNodesTitle": "Nœuds Disponibles", "upcomingNodesTitle": "Nœuds à Venir", "ctaMessage": "Commencez à créer des flux de travail maintenant pour étendre les capacités de votre chatbot !", "soonMessage": "Bientôt disponible."}, "arrangeNodes": "Auto Layout", "nodesArranged": "Layout appliqué avec succès", "visibilityUpdated": "Visibilité mise à jour avec succès"}}