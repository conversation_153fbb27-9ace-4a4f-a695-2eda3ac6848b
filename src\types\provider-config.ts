/**
 * TypeScript interfaces for provider API key configurations
 * Supporting BYOK (Bring Your Own Key) provider management
 */

export interface ModelConfig {
  apiName: string;
  uiName: string;
  supportsTools: boolean;
  apiVersion?: string; // Required for Azure OpenAI
  contextWindow?: number;
  maxTokens?: number;
}

export interface ProviderConfig {
  id: string;
  providerName: string;
  displayName: string;
  baseUrl?: string;
  models: ModelConfig[];
  isEnabled: boolean;
  providerType: "openai-compatible" | "azure-openai";
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  status?: "active" | "error" | "testing";
}

export interface ProviderConfigEntity {
  id: string;
  providerName: string;
  displayName: string;
  baseUrl?: string;
  apiKey: string;
  models: ModelConfig[];
  isEnabled: boolean;
  providerType: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export type RuntimeProviderConfig = ProviderConfigEntity;

export interface ProviderConfigFile {
  version: string;
  providers: ProviderConfigEntity[];
  settings: {
    autoBackup: boolean;
    encryptionEnabled: boolean;
  };
}

export interface CreateProviderRequest {
  providerName: string;
  displayName: string;
  baseUrl?: string;
  apiKey: string;
  models: ModelConfig[];
  providerType: "openai-compatible" | "azure-openai";
  metadata?: Record<string, any>;
}

export interface UpdateProviderRequest extends Partial<CreateProviderRequest> {
  isEnabled?: boolean;
}

export interface GetProvidersResponse {
  providers: ProviderConfig[];
}

export interface TestProviderResponse {
  success: boolean;
  availableModels?: string[];
  error?: string;
  latency?: number;
}

export enum ProviderErrorType {
  INVALID_API_KEY = "invalid_api_key",
  NETWORK_ERROR = "network_error",
  INVALID_BASE_URL = "invalid_base_url",
  RATE_LIMITED = "rate_limited",
  PROVIDER_UNAVAILABLE = "provider_unavailable",
  ENCRYPTION_ERROR = "encryption_error",
  VALIDATION_ERROR = "validation_error",
  FILE_ACCESS_ERROR = "file_access_error",
  BACKUP_ERROR = "backup_error",
}

export interface ProviderError {
  type: ProviderErrorType;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}