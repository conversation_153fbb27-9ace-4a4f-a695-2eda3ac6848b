{"name": "friday", "version": "1.20.2", "private": true, "author": "cgoinglove", "license": "MIT", "type": "module", "main": "electron/main.js", "scripts": {"dev": "next dev --turbopack", "dev:https": "next dev --turbopack --experimental-https", "build": "next build", "start": "node .next/standalone/server.js", "build:prod": "next build && node scripts/fix-standalone.cjs", "start:prod": "pnpm build:prod && node .next/standalone/server.js", "build:desktop": "node scripts/build-desktop.js", "create:desktop": "node scripts/create-desktop-app.js", "build:local": "cross-env NO_HTTPS='1' next build", "test": "vitest run", "test:watch": "vitest", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "check-types": "tsc --noEmit", "initial:env": "tsx scripts/initial-env.ts", "openai-compatiable:init": "tsx scripts/init-openai-compatiable.ts", "openai-compatiable:parse": "tsx scripts/parse-openai-compatiable.ts", "postinstall": "tsx scripts/postinstall.ts", "clean": "tsx scripts/clean.ts", "db:generate": "drizzle-kit generate", "db:reset": "drizzle-kit drop && drizzle-kit push", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:migrate": "tsx scripts/db-migrate.ts", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "docker-compose:up": "docker-compose -f docker/compose.yml up -d --build", "docker-compose:down": "docker-compose -f docker/compose.yml down", "docker-compose:logs": "docker-compose -f docker/compose.yml logs -f", "docker-compose:ps": "docker-compose -f docker/compose.yml ps", "docker-compose:update": "git pull && docker-compose -f docker/compose.yml up -d --build", "docker:pg": "docker run --name friday-pg -e POSTGRES_PASSWORD=your_password -e POSTGRES_USER=your_username -e POSTGRES_DB=your_database_name -p 5432:5432 -d postgres", "docker:redis": "docker run --name friday-redis -p 6379:6379 -d redis:7-alpine", "docker:app": "docker build -f docker/Dockerfile -t friday . && docker run -p 3000:3000 -e NO_HTTPS=1 friday", "prepare": "husky", "check": "pnpm lint:fix && pnpm check-types && pnpm test", "electron": "electron .", "electron:dev": "concurrently \"pnpm dev\" \"wait-on http://localhost:3000 && electron .\"", "electron:build": "pnpm build && node scripts/fix-standalone.cjs && electron-builder", "dist": "pnpm electron:build"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/openai-compatible": "^0.2.16", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.18", "@modelcontextprotocol/sdk": "^1.17.1", "@openrouter/ai-sdk-provider": "^0.4.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-mention": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@tiptap/suggestion": "^2.26.1", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.8.2", "ai": "^4.3.19", "bcrypt-ts": "^7.1.0", "better-auth": "^1.3.4", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "consola": "^3.4.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "dotenv": "^16.6.1", "drizzle-orm": "^0.41.0", "emoji-picker-react": "^4.13.2", "framer-motion": "^12.23.12", "hast-util-to-jsx-runtime": "^2.3.6", "ioredis": "^5.7.0", "json-schema": "^0.4.0", "lucide-react": "^0.486.0", "mermaid": "^11.9.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "ogl": "^1.0.11", "ollama-ai-provider": "^1.2.0", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.9.2", "sonner": "^2.0.7", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "ts-edge": "^1.0.4", "ts-safe": "^0.0.5", "tw-animate-css": "^1.3.6", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.1.11", "@types/json-schema": "^7.0.15", "@types/node": "^20.19.9", "@types/pg": "^8.15.5", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "electron": "^37.2.6", "electron-builder": "^26.0.12", "eslint": "^9.32.0", "eslint-config-next": "15.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.4", "nativefier": "^52.0.0", "rimraf": "^6.0.1", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "wait-on": "^8.0.4"}, "lint-staged": {"*.{js,json,mjs,ts,yaml,tsx,css}": ["pnpm format", "pnpm lint:fix"]}, "packageManager": "pnpm@10.2.1", "engines": {"node": ">=18"}, "build": {"appId": "com.friday.ai", "productName": "Friday AI", "directories": {"output": "dist"}, "files": ["electron/**/*", ".next/standalone/**/*", "public/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": ".next/static", "to": ".next/static"}], "win": {"target": "nsis", "icon": "electron/assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@tailwindcss/oxide", "esbuild", "sharp", "unrs-resolver"], "ignoredBuiltDependencies": ["electron", "electron-winstaller"]}}