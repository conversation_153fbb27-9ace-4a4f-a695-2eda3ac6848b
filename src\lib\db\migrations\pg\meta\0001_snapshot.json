{"id": "613e110e-b4ef-4b11-940c-66ee3f81cc27", "prevId": "0ffa558d-fb55-4fab-a8d1-a1fba02c4799", "version": "7", "dialect": "postgresql", "tables": {"public.chat_message": {"name": "chat_message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "thread_id": {"name": "thread_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json[]", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "json[]", "primaryKey": false, "notNull": false}, "annotations": {"name": "annotations", "type": "json[]", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"chat_message_thread_id_chat_thread_id_fk": {"name": "chat_message_thread_id_chat_thread_id_fk", "tableFrom": "chat_message", "tableTo": "chat_thread", "columnsFrom": ["thread_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_thread": {"name": "chat_thread", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chat_thread_user_id_user_id_fk": {"name": "chat_thread_user_id_user_id_fk", "tableFrom": "chat_thread", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_server_binding": {"name": "mcp_server_binding", "schema": "", "columns": {"owner_type": {"name": "owner_type", "type": "text", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"mcp_server_binding_owner_type_owner_id_pk": {"name": "mcp_server_binding_owner_type_owner_id_pk", "columns": ["owner_type", "owner_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructions": {"name": "instructions", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"project_user_id_user_id_fk": {"name": "project_user_id_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}