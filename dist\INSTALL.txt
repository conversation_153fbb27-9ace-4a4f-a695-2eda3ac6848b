FRIDAY AI ASSISTANT - INSTALLATION GUIDE
========================================

QUICK INSTALL (Recommended):
1. Download the distribution package
2. Extract all files to a folder of your choice
3. Double-click "Friday-AI-Assistant-v1.20.1.exe"
4. Wait for the application to start (10-30 seconds)
5. Enjoy your AI assistant!

DETAILED STEPS:
===============

Step 1: Download
- Ensure you have the complete distribution package
- Verify all files are present (see DISTRIBUTION-SUMMARY.md)

Step 2: Choose Location
- Create a folder like "C:\Friday AI Assistant\"
- Or use "C:\Program Files\Friday AI Assistant\"
- Or any location you prefer

Step 3: Extract Files
- Copy all files from the distribution package
- Ensure "Friday-AI-Assistant-v1.20.1.exe" is included
- Keep all documentation files for reference

Step 4: First Run
- Double-click the executable file
- Windows may show a security warning (normal for new apps)
- Click "More info" then "Run anyway" if prompted
- Wait for the application to initialize

Step 5: Verify Installation
- Application window should appear
- Check that all features are working
- Refer to README.md for feature overview

SYSTEM REQUIREMENTS:
===================
✓ Windows 10 or later (64-bit)
✓ 4GB RAM minimum
✓ 200MB free disk space
✓ Internet connection for AI features

TROUBLESHOOTING:
===============

Application won't start:
- Check Windows version (must be Windows 10+)
- Ensure sufficient disk space
- Try running as administrator
- Check antivirus software isn't blocking

Performance issues:
- Close other applications to free memory
- Ensure stable internet connection
- Check available RAM (4GB minimum)

Security warnings:
- Application is safe but unsigned
- Add exception in antivirus if needed
- Windows Defender may require approval

UNINSTALL:
==========
- Simply delete the application folder
- No registry entries or system files to remove
- Application is completely portable

UPDATES:
========
- Download new version when available
- Replace old executable with new one
- Keep your data and settings

SUPPORT:
========
- Read README.md for detailed information
- Check VERSION.txt for technical details
- Review DISTRIBUTION-SUMMARY.md for overview

NO INSTALLATION REQUIRED - JUST RUN AND ENJOY!
