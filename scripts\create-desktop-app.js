#!/usr/bin/env node

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import http from 'http';

const PORT = 3000;
const APP_NAME = 'Friday AI';
const OUTPUT_DIR = 'desktop-build';

console.log('🚀 Creating Friday AI Desktop Application...');

// Function to check if server is running
function checkServer(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 1000
    }, () => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Function to create desktop app with nativefier
async function createDesktopApp() {
  console.log('🖥️ Creating desktop application with nativefier...');
  
  const logoPath = path.join(process.cwd(), 'public', 'logo.png');
  const outputPath = path.join(process.cwd(), OUTPUT_DIR);
  
  // Clean output directory
  if (fs.existsSync(outputPath)) {
    fs.rmSync(outputPath, { recursive: true, force: true });
  }
  
  const nativefierArgs = [
    `http://localhost:${PORT}`,
    outputPath,
    '--name', APP_NAME,
    '--platform', 'windows',
    '--arch', 'x64',
    '--electron-version', '31.0.0',
    '--single-instance',
    '--disable-dev-tools',
    '--hide-window-frame',
    '--maximize',
    '--show-menu-bar',
    '--internal-urls', '.*',
    '--file-download-options', '{"saveAs": true}',
    '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  ];
  
  // Add icon if logo exists
  if (fs.existsSync(logoPath)) {
    nativefierArgs.push('--icon', logoPath);
    console.log('🎨 Using logo.png as application icon');
  }
  
  return new Promise((resolve, reject) => {
    const nativefierProcess = spawn('npx', ['nativefier', ...nativefierArgs], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });
    
    nativefierProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Desktop application created successfully!');
        console.log(`📁 Output directory: ${outputPath}`);
        resolve();
      } else {
        reject(new Error(`Nativefier failed with code ${code}`));
      }
    });
  });
}

// Main function
async function main() {
  try {
    // Check if server is running
    console.log(`⏳ Checking if server is running on port ${PORT}...`);
    const isRunning = await checkServer(PORT);
    
    if (!isRunning) {
      console.log('❌ Server is not running!');
      console.log('');
      console.log('Please start the development server first:');
      console.log('  pnpm dev');
      console.log('');
      console.log('Then run this script again.');
      process.exit(1);
    }
    
    console.log('✅ Server is running!');
    
    // Create desktop app
    await createDesktopApp();
    
    console.log('🎉 Desktop application created successfully!');
    console.log(`📂 Find your executable in: ${path.join(process.cwd(), OUTPUT_DIR)}`);
    console.log('');
    console.log('📝 Instructions:');
    console.log('1. To use the desktop app:');
    console.log('   a. Open a terminal and run: pnpm dev');
    console.log('   b. Wait for the server to start on http://localhost:3000');
    console.log('   c. Launch the desktop app from the created folder');
    console.log('2. The desktop app will connect to your local development server');
    console.log('3. Keep the development server running while using the desktop app');
    console.log('4. You can sign up/sign in normally in the desktop app');
    console.log('5. The app uses your Supabase database for authentication and data');
    
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Build interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Build terminated');
  process.exit(0);
});

main();
