{"Common": {"cancel": "Cancel", "update": "Update", "continue": "Continue", "success": "Success", "delete": "Delete", "save": "Save", "back": "Back", "next": "Next", "create": "Create", "showLess": "Show less", "showMore": "Show more", "generate": "Generate", "edit": "Edit", "editAgent": "Edit Agent", "search": "Search...", "approve": "Approve", "reject": "Reject", "saving": "Saving...", "optional": "Optional", "deleting": "Deleting...", "run": "Run", "description": "Description", "defaultValue": "Default Value", "empty": "Empty", "required": "Required", "options": "Options", "status": "Status", "result": "Result", "startedAt": "Started At", "duration": "Duration", "addOption": "Add Option", "tool": "Tool", "selectTool": "Select Tool...", "noResults": "No results.", "generateWithAI": "Generate With AI", "generateInputWithAI": "Generate Input With AI", "generatingInputWithAI": "Generating input with AI...", "inputGeneratedSuccessfully": "Input generated successfully", "failedToGenerateInput": "Failed to generate input", "createWithExample": "Create With Example", "resultsFound": "{count} results found", "youAreAnExpertIn": "You are an expert in {role}", "sharedBy": "Shared by {userName}"}, "Error": {}, "Info": {"mcpAddingDisabled": "MCP server addition has been disabled by the administrator.", "vercelSyncDelay": "Running on Vercel\n\nMCP changes may take 10-15 seconds to sync. Please wait a moment after adding, editing, or deleting servers if changes don't appear immediately."}, "Workflow": {"title": "Workflow", "whatIsWorkflow": "What is a Workflow?", "myWorkflows": "My Workflows", "sharedWorkflows": "Shared Workflows", "createWorkflow": "Create Workflow", "draft": "Draft", "publish": "Publish", "createWorkflowDescription": "Create workflows as powerful tools for your chatbot.", "workflowDescription": "These can be triggered during conversations to automate complex tasks.", "nameAndIcon": "Name And Icon", "workflowNamePlaceholder": "Chat<PERSON> will recognize this as tool name", "description": "Description", "descriptionPlaceholder": "<PERSON><PERSON><PERSON> will see this as tool description", "inputNodeCannotBeDeleted": "Input node cannot be deleted", "autoSaveDescription": "Auto saved every 10 seconds", "draftDescription": "Currently in Draft.\n\nClick Publish to make it available to chatbot\n(but no longer editable).", "publishedDescription": "Currently Published and available to chatbot.\n\nClick Draft to make it editable\n(but unavailable to chatbot).", "private": "Private", "readonly": "Read Only", "public": "Public", "privateDescription": "Only you can view, edit, and use this workflow as a tool.", "readonlyDescription": "Others can view and use as a tool, but only you can edit.", "publicDescription": "Anyone can view, edit, and use this workflow as a tool.", "visibilityDescription": "Control who can access and modify this workflow", "nodeDescriptionPlaceholder": "node description...", "nextNode": "Next Node", "nextNodeDescription": "Add a next node to this workflow.", "addNextNode": "Add Next Node", "inputFields": "Input Fields", "addInputField": "Add Input Field", "inputFieldsDescription": "Define the parameter schema for this workflow.\n\nWhen the chatbot uses this as a tool,\nit will provide values according to this schema.", "fieldEditor": "Field Editor", "variableName": "Variable Name", "variableNamePlaceholder": "Enter variable name...", "fieldDescriptionPlaceholder": "Enter field description...", "defaultValuePlaceholder": "Enter default {type} value...", "selectOptionPlaceholder": "Select option...", "unlink": "Unlink Node", "elseIfDescription": "If the condition is not met, the logic to be executed is defined.", "elseDescription": "If the condition is not met, the logic to be executed is defined.", "addCondition": "Add Condition", "noVariablesFound": "No variables found", "outputVariables": "Output Variables", "outputVariablesDescription": "Output variables are the variables that are output from the workflow.", "addOutputVariable": "Add Output Variable", "outputSchema": "Output Schema", "addMessage": "Add Message", "messagesDescription": "Generate data through LLM processing.\n\nUse '/' to mention and reference data from previous nodes as input.\n\nWith Structured Output enabled, perfect for data transformation, formatting, and validation.", "descriptionAndSchema": "Description & Schema", "noDescriptionAndSchema": "No description and schema", "toolDescription": "Provide information needed for LLM to generate tool parameters.\n\nUse '/' to mention data from previous nodes.", "generateInputWithAIDescription": "Write a prompt to generate input for the workflow", "selectVariable": "Select Variable", "structuredOutput": "Structured Output", "structuredOutputDescription": "Generate response as JSON object with defined schema", "outputSchemaEditor": "Output Schema Editor", "addField": "Add Field", "saveSchema": "<PERSON>", "generateSchemaWithAI": "Generate Schema with AI", "describeOutputDataRequest": "Provide example JSON data that represents what this node should output\n\nExample: {eg}", "generatingJsonSchemaWithAI": "Generating JSON Schema with AI...", "jsonSchemaGeneratedSuccessfully": "JSON Schema generated successfully!", "failedToGenerateSchema": "Failed to generate schema", "jsonSchemaEditorDescription": "Direct JSON Schema editing with AI assistance. Supports complex nested structures and arrays.", "template": "Template", "templateDescription": "Generate template documents.\n\nUse '/' to reference and use output values from other nodes.", "greeting": {"buildAutomationTitle": "Build Automation by Connecting Nodes", "buildAutomationDescription": "Connect various nodes to automate complex tasks. Each node handles specific functions, and data flows sequentially for processing.", "chatbotToolTitle": "Use as <PERSON><PERSON><PERSON> Tools", "chatbotToolDescription": "The main purpose of workflows is to use them as tools in chatbot conversations. Turn repetitive tasks into workflows for easy execution during chats.", "parameterBasedTitle": "Parameter-Based Start", "parameterBasedDescription": "Input nodes define parameter structures, not triggers. They specify the data format needed when chatbot calls this workflow as a tool.", "exampleTitle": "Usage Example", "exampleDescription": "Create an \"Email Writing → Translation → Send\" workflow, then easily execute it in chatbot conversations with \"@email_workflow\".", "availableNodesTitle": "Available Nodes", "upcomingNodesTitle": "Upcoming Nodes", "ctaMessage": "Start creating workflows now to expand your chatbot's capabilities!", "soonMessage": "Coming soon."}, "example": {"babyResearch": "Baby Research", "getWeather": "Get Weather"}, "kindsDescription": {"input": "Define input parameters that the chatbot will provide when using this workflow as a tool.\n\nSpecify the data structure and validation rules for tool execution.", "output": "Collect and return the final results from your workflow execution.\n\nCombine data from multiple nodes into the final tool response.", "llm": "Generate text or structured data using AI models.\n\nReference previous node outputs with '/' mentions to create context-aware responses.\n\nUse Structured Output to transform, format, and validate data - not just for text generation.", "tool": "Execute MCP tools or external services.\n\nWrite instructions in messages, and LLM will generate the required tool parameters from your context.", "note": "Add documentation and comments to organize your workflow logic.\n\nHelp team members understand complex workflow processes.", "code": "Execute custom code scripts with access to previous node data.\n\nRun JavaScript, Python, or other languages within your workflow (coming soon).", "http": "Fetch data from external APIs and web services via HTTP requests.\n\nIntegrate with REST APIs, webhooks, and third-party services.", "template": "Create dynamic documents by combining text with data from previous nodes.\n\nGenerate emails, reports, or formatted content using variable substitution.", "condition": "Add conditional logic to branch your workflow based on data evaluation.\n\nCreate if-else logic to handle different scenarios and data conditions."}, "structuredOutputSwitchConfirm": "You currently have structured output enabled.\n What would you like to do?", "structuredOutputSwitchConfirmOk": "Edit Structured Output", "structuredOutputSwitchConfirmCancel": "Change to Text Output", "noTools": "No published workflows available.\nCreate workflows to build custom tools.", "arrangeNodes": "Auto Layout", "nodesArranged": "Layout applied successfully", "visibilityUpdated": "Visibility updated successfully"}, "Auth": {"SignIn": {"title": "Welcome Back", "description": "Sign in to continue to your account", "oauthClientIdNotSet": "{provider} client ID is not set", "noAccount": "Don't have an account? ", "signUp": "Sign up", "signIn": "Sign in", "orContinueWith": "OR CONTINUE WITH"}, "SignUp": {"title": "Create an account", "signIn": "Sign in", "description": "Sign up to your account", "step1": "Start your journey with us by entering your email address", "step2": "I'll use this name when we chat", "step3": "Create a strong password to secure your account", "signUp": "Sign Up", "invalidEmail": "Invalid email address", "emailAlreadyExists": "Email already exists", "nameRequired": "Name is required", "passwordRequired": "Password is required", "createAccount": "Create account"}, "Intro": {"description": "Welcome to better-chatbot. Sign in to experience our AI-powered conversational tools."}}, "Chat": {"Error": "<PERSON><PERSON>", "thisMessageWasNotSavedPleaseTryTheChatAgain": "This message was not saved. Please try the chat again.", "Greeting": {"goodMorning": "Good morning, {name}", "goodAfternoon": "Good afternoon, {name}", "goodEvening": "Good evening, {name}", "niceToSeeYouAgain": "Nice to see you again, {name}", "whatAreYouWorkingOnToday": "What are you working on today? {name}", "letMeKnowWhenYoureReadyToBegin": "Let me know when you're ready to begin.", "whatAreYourThoughtsToday": "What are your thoughts today?", "whereWouldYouLikeToStart": "Where would you like to start?", "whatAreYouThinking": "What are you thinking? {name}"}, "TemporaryChat": {"toggleTemporaryChat": "Toggle Temporary Chat", "temporaryChat": "Temporary Chat", "resetChat": "<PERSON><PERSON>", "thisChatWontBeSaved": "This chat won't be saved.", "feelFreeToAskAnythingTemporarily": "Feel free to ask anything temporarily", "temporaryChatInstructions": "Temporary Chat Instructions", "temporaryChatInstructionsPlaceholder": "Enter your instructions here", "temporaryChatInstructionsDescription": "You can set instructions for the temporary chat. This will be used as a system prompt for the temporary chat."}, "placeholder": "Ask anything or @mention", "Tool": {"webSearching": "Searching the Web...", "searchedTheWeb": "Searched the Web", "toolsSetupDescription": "Select tools that the chatbot can use.\nThe chatbot will use selected tools based on its own judgment.\n\nYou can also force the use of specific tools through @mention.", "selectToolMode": "Select a tool mode", "autoToolModeDescription": "Decides when to use tools without asking you", "manualToolModeDescription": "Asks your permission before using any tools", "noneToolModeDescription": "Do not use tools. @mention is still available.", "toolsSetup": "Tools Setup", "preset": "Preset", "toolPresets": "Tool Presets", "saveAsPreset": "Save As Preset", "saveAsPresetDescription": "Save the current tool configuration as a preset.", "noPresetsAvailableYet": "No presets available yet", "presetNameCannotBeEmpty": "Preset name cannot be empty", "presetNameAlreadyExists": "Preset name already exists", "presetSaved": "Preset saved", "clickSaveAsPresetToGetStarted": "Click Save As Preset to get started.", "searchOptions": "Search Options", "searchOptionsDescription": "You can pass search options to the chatbot, such as the maximum number of search results, the search date, etc.", "defaultToolKit": {"visualization": "Data Visualization", "webSearch": "Search the Web", "http": "HTTP Request"}}, "VoiceChat": {"title": "Voice Chat Mode", "compactDisplayMode": "Compact display mode", "conversationDisplayMode": "Conversation display mode", "pleaseCloseTheVoiceChatAndTryAgain": "Please close the voice chat and try again.", "startConversation": "Start conversation", "closeMic": "Close Mic", "openMic": "Open Mic", "endConversation": "End conversation", "toggleVoiceChat": "Toggle Voice Chat", "readyWhenYouAreJustStartTalking": "Ready when you are—just start talking.", "yourMicIsOff": "Your mic is off.", "preparing": "Preparing...", "startVoiceChat": "Start voice chat?"}, "Thread": {"chat": "Cha<PERSON>", "renameChat": "Re Name", "deleteChat": "Delete Chat", "deleteUnarchivedChats": "Delete All Unarchived Chats", "confirmDeleteUnarchivedChats": "Are you sure you want to delete all unarchived chats?", "thisActionCannotBeUndone": "This action cannot be undone.", "unarchivedChatsDeleted": "Unarchived chats have been deleted", "failedToDeleteUnarchivedChats": "Failed to delete unarchived chats", "failedToDeleteThread": "Failed to delete thread", "threadDeleted": "Thread deleted", "failedToUpdateThread": "Failed to update thread", "titleRequired": "Title is required", "threadUpdated": "Thread updated", "areYouSureYouWantToDeleteThisChatThread": "Are you sure you want to delete this Chat thread?"}, "ChatPreferences": {"title": "Chat Preferences", "whatShouldWeCallYou": "What should we call you?", "botName": "Assistant Name", "whatBestDescribesYourWork": "What best describes your work?", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "What personal preferences should be taken into account in responses?", "responseStyleExample1": "e.g. keep explanations brief and to the point", "responseStyleExample2": "e.g. when learning new concepts, I find analogies particularly helpful", "responseStyleExample3": "e.g. ask clarifying questions before giving detailed answers", "responseStyleExample4": "e.g. remember I primarily code in Python (not a coding beginner)", "professionExample1": "e.g. software engineer", "professionExample2": "e.g. product manager", "professionExample3": "e.g. marketing manager", "professionExample4": "e.g. sales manager", "professionExample5": "e.g. business analyst", "preferencesSaved": "Preferences saved", "failedToSavePreferences": "Failed to save preferences", "userInstructions": "User Instructions", "userInstructionsDescription": "Introduce yourself and get a more personalized response.", "mcpInstructions": "MCP Instructions", "mcpInstructionsDescription": "Customize the MCP server instructions."}}, "Layout": {"workflow": "Workflow", "toggleSidebar": "Toggle Sidebar", "newChat": "New Chat", "mcpConfiguration": "MCP Configuration", "providerManagement": "Provider Management", "agents": "Agents", "newAgent": "Create Agent", "createAgent": "Create an agent", "createYourOwnAgent": "Create your own specialized AI agent with unique features and personality", "createYourOwnAgentOrSelectShared": "Create your own specialized AI agent or select from shared agents in the Agents page", "whatIsAgent": "What is Agent?", "agentDescription": "Agents are specialized AI assistants that can be customized with specific roles, instructions, and tools to help you with various tasks.", "specializedAIAssistant": "Specialized AI Assistant", "specializedAIAssistantDescription": "Each agent can be customized with specific roles, personalities, and expertise areas to provide focused assistance for your unique needs.", "customInstructions": "Custom Instructions", "customInstructionsDescription": "Define detailed system prompts and behavioral guidelines to shape how your agent responds and interacts with you.", "toolIntegration": "Tool Integration", "toolIntegrationDescription": "Connect your agents to MCP servers, workflows, and other tools to extend their capabilities beyond conversation.", "agentExamples": "Agent Examples", "businessAssistant": "Business Assistant", "businessAssistantDescription": "Specialized in business analysis, report generation, and professional communication.", "creativeWriter": "Creative Writer", "creativeWriterDescription": "Focused on storytelling, content creation, and creative brainstorming.", "technicalExpert": "Technical Expert", "technicalExpertDescription": "Equipped with development tools and coding expertise for technical tasks.", "createFirstAgentToStart": "Create your first agent to get started!", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last 7 days", "older": "Older", "recentChats": "Recent Chats", "deleteAllChats": "Delete All Chats", "deleteUnarchivedChats": "Delete Unarchived <PERSON><PERSON>", "noConversationsYet": "No conversations yet", "deletingAllChats": "Deleting all threads...", "deletingUnarchivedChats": "Deleting unarchived threads...", "allChatsDeleted": "All threads deleted", "unarchivedChatsDeleted": "Unarchived threads deleted", "failedToDeleteAllChats": "Failed to delete all threads", "failedToDeleteUnarchivedChats": "Failed to delete unarchived threads", "chatPreferences": "Chat Preferences", "keyboardShortcuts": "Keyboard Shortcuts", "theme": "Theme", "signOut": "Sign out", "language": "Language", "showAllChats": "View All Chats", "showLessChats": "Show less", "reportAnIssue": "Report an issue", "joinCommunity": "Join Community"}, "Archive": {"title": "Archive", "addArchive": "Add Archive", "archiveName": "Archive Name", "archiveDescription": "Archive Description", "archiveDescriptionPlaceholder": "Archives are spaces to store chat history.", "noArchives": "No archives", "createFirstArchive": "Create your first archive", "archiveCreated": "Archive created", "archiveUpdated": "Archive updated", "archiveDeleted": "Archive deleted", "failedToCreateArchive": "Failed to create archive", "failedToUpdateArchive": "Failed to update archive", "failedToDeleteArchive": "Failed to delete archive", "editArchive": "Edit Archive", "editArchiveDescription": "Edit archive information", "deleteArchive": "Delete Archive", "confirmDeleteArchive": "Are you sure you want to delete this archive?", "deleteArchiveDescription": "This archive and all its items will be permanently deleted. This action cannot be undone.", "addToArchive": "Add to Archive", "removeFromArchive": "Remove from Archive", "itemAddedToArchive": "Item added to archive", "itemRemovedFromArchive": "Item removed from archive"}, "Agent": {"title": "Agent", "newAgent": "Create Agent", "generatingAgent": "Generating Agent...", "agentNameAndIconLabel": "Give your agent a name and icon.", "agentDescriptionLabel": "Add a brief description of what this agent does.", "agentDescriptionPlaceholder": "This is just a description of the agent, it's not critical.", "agentSettingsDescription": "From here, these are settings that can affect the agent.", "thisAgentIs": "This agent is an expert in", "expertIn": "", "agentRolePlaceholder": "stock analysis", "agentInstructionsLabel": "Feel free to write the agent's role, personality, guidelines, knowledge, etc.", "agentInstructionsPlaceholder": "This agent helps with stock analysis. It uses web search tools to obtain stock information...", "agentToolsLabel": "Add tools that this agent can use.", "loadingTools": "Loading tools...", "addTools": "Please add tools.", "generateAgentGreeting": "Hello! I'll help you create your own agent. What would you like to create?", "generateAgentDetailedGreeting": "Hello! I'll help you create your own agent. What would you like to create? You can write briefly or in detail.", "inputPromptHere": "input prompt here...", "agentNamePlaceholder": "better-agent", "myAgents": "My Agents", "sharedAgents": "Shared Agents", "noAgents": "No agents yet", "createFirst": "Create your first agent to get started", "noSharedAgents": "No shared agents", "noSharedAgentsDescription": "No public agents are available to bookmark", "noDescription": "No description provided", "bookmarkAdded": "Agent bookmarked", "bookmarkRemoved": "Bookmark removed", "bookmarkedAgent": "Bookmarked agent", "addBookmark": "Bookmark agent", "removeBookmark": "Remove bookmark", "visibilityUpdated": "Visibility updated", "deleted": "Agent deleted", "created": "Agent created successfully", "updated": "Agent updated successfully", "deleteConfirm": "Are you sure you want to delete this agent?", "makePrivate": "Make Private", "makeReadonly": "Make Read Only", "makePublic": "Make Public", "visibility": "Visibility", "private": "Private", "readOnly": "Read Only", "public": "Public", "privateDescription": "Only you can view, edit, and use this agent.", "readOnlyDescription": "Others can view and use as a tool, but only you can edit.", "publicDescription": "Anyone can view, edit, and use this agent as a tool."}, "KeyboardShortcuts": {"title": "Keyboard Shortcuts", "newChat": "New Chat", "toggleTemporaryChat": "Toggle Temporary Chat", "toggleSidebar": "Toggle Sidebar", "toolMode": "Tool Mode", "lastMessageCopy": "Copy Last Message", "openChatPreferences": "Open Chat Preferences", "deleteThread": "Delete Chat", "openShortcutsPopup": "Open Shortcuts Popup", "toggleVoiceChat": "Toggle Voice Chat"}, "MCP": {"marketplace": "Marketplace", "addMcpServer": "Add Server", "configureYourMcpServerConnectionSettings": "Configure your MCP server connection settings", "mcpConfiguration": "MCP Configuration", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "Name must contain only alphanumeric characters (A-Z, a-z, 0-9) and hyphens (-)", "nameIsRequired": "Name is required", "configurationSavedSuccessfully": "Configuration saved successfully", "enterMcpServerName": "Enter MCP server name", "saveConfiguration": "Save Configuration", "toolsTest": "Tools Test", "refresh": "Refresh", "delete": "Delete", "edit": "Edit", "configuration": "Configuration", "availableTools": "Available Tools", "noToolsAvailable": "No tools available", "overviewTitle": "Connect Your First Server", "overviewDescription": "Add MCP servers to unlock powerful AI integrations", "searchTools": "Search tools", "detail": "Detail", "noSchemaPropertiesAvailable": "No schema properties available", "createInputWithAI": "Create Input with AI", "generateExampleInputJSON": "Generate Example Input JSON", "enterPromptToGenerateExampleInputJSON": "Enter a prompt to generate example input JSON for the selected tool.", "callTool": "Call Tool", "customInstructions": "Custom instructions", "serverCustomInstructionsPlaceholder": "These lines will be added to the AI system prompt whenever a tool from this server is available.", "nameAlreadyExists": "Name already exists", "additionalInstructions": "Tool Customization Instructions", "inputSchema": "Input Schema", "toolCustomizationInstructions": "Tool customization instructions will be added to the system prompt when the tool is available.\nexample) Always enter the email in <NAME_EMAIL>.", "mcpServerCustomization": "MCP Customization", "mcpServerCustomizationDescription": "MCP server customization instructions will be added to the system prompt when the MCP server is available.", "toolCustomizationInstructionsPlaceholder": "Tool customization instructions are not available.", "mcpServerCustomizationPlaceholder": "eg. If the input value is email, always enter the email in <NAME_EMAIL>."}}