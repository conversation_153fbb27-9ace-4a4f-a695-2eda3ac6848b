"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "ui/button";
import { Input } from "ui/input";
import { Label } from "ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "ui/card";
import { Badge } from "ui/badge";
import {
  Loader,
  Plus,
  Settings,
  Trash2,
  TestTube,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { toast } from "sonner";
import {
  ProviderConfig,
  CreateProviderRequest,
  UpdateProviderRequest,
  TestProviderResponse,
  ModelConfig,
} from "@/types/provider-config";

interface ProviderFormData {
  providerName: string;
  displayName: string;
  baseUrl: string;
  apiKey: string;
  providerType: "openai-compatible" | "azure-openai";
  models: ModelConfig[];
}

const DEFAULT_MODELS: Record<string, ModelConfig[]> = {
  openai: [
    {
      apiName: "gpt-4o",
      uiName: "GPT-4o",
      supportsTools: true,
      contextWindow: 128000,
    },
    {
      apiName: "gpt-4o-mini",
      uiName: "GPT-4o Mini",
      supportsTools: true,
      contextWindow: 128000,
    },
    {
      apiName: "gpt-4-turbo",
      uiName: "GPT-4 Turbo",
      supportsTools: true,
      contextWindow: 128000,
    },
    {
      apiName: "gpt-4",
      uiName: "GPT-4",
      supportsTools: true,
      contextWindow: 8192,
    },
    {
      apiName: "o1",
      uiName: "o1",
      supportsTools: false,
      contextWindow: 200000,
    },
    {
      apiName: "o1-mini",
      uiName: "o1 Mini",
      supportsTools: false,
      contextWindow: 128000,
    },
    {
      apiName: "o1-preview",
      uiName: "o1 Preview",
      supportsTools: false,
      contextWindow: 128000,
    },
  ],
  anthropic: [
    {
      apiName: "claude-opus-4-1-20250805",
      uiName: "Claude Opus 4.1",
      supportsTools: true,
      contextWindow: 200000,
    },
    {
      apiName: "claude-opus-4-20250514",
      uiName: "Claude Opus 4",
      supportsTools: true,
      contextWindow: 200000,
    },
    {
      apiName: "claude-sonnet-4-20250514",
      uiName: "Claude Sonnet 4",
      supportsTools: true,
      contextWindow: 200000,
    },
    {
      apiName: "claude-3-7-sonnet-20250219",
      uiName: "Claude 3.7 Sonnet",
      supportsTools: true,
      contextWindow: 200000,
    },
    {
      apiName: "claude-3-5-sonnet-20241022",
      uiName: "Claude 3.5 Sonnet",
      supportsTools: true,
      contextWindow: 200000,
    },
    {
      apiName: "claude-3-5-haiku-20241022",
      uiName: "Claude 3.5 Haiku",
      supportsTools: true,
      contextWindow: 200000,
    },
  ],
  google: [
    {
      apiName: "gemini-2.5-pro",
      uiName: "Gemini 2.5 Pro",
      supportsTools: true,
      contextWindow: 2000000,
    },
    {
      apiName: "gemini-2.5-flash",
      uiName: "Gemini 2.5 Flash",
      supportsTools: true,
      contextWindow: 1000000,
    },
    {
      apiName: "gemini-2.5-flash-lite",
      uiName: "Gemini 2.5 Flash Lite",
      supportsTools: true,
      contextWindow: 1000000,
    },
    {
      apiName: "gemini-2.0-flash",
      uiName: "Gemini 2.0 Flash",
      supportsTools: true,
      contextWindow: 1000000,
    },
  ],
  xai: [
    {
      apiName: "grok-beta",
      uiName: "Grok Beta",
      supportsTools: true,
      contextWindow: 131072,
    },
    {
      apiName: "grok-2-1212",
      uiName: "Grok 2 (1212)",
      supportsTools: true,
      contextWindow: 131072,
    },
    {
      apiName: "grok-2-vision-1212",
      uiName: "Grok 2 Vision",
      supportsTools: true,
      contextWindow: 131072,
    },
    {
      apiName: "grok-2-latest",
      uiName: "Grok 2 Latest",
      supportsTools: true,
      contextWindow: 131072,
    },
    {
      apiName: "grok-2",
      uiName: "Grok 2",
      supportsTools: true,
      contextWindow: 131072,
    },
  ],
  groq: [
    {
      apiName: "llama-3.1-70b-versatile",
      uiName: "Llama 3.1 70B",
      supportsTools: true,
      contextWindow: 131072,
    },
    {
      apiName: "llama-3.1-8b-instant",
      uiName: "Llama 3.1 8B",
      supportsTools: true,
      contextWindow: 131072,
    },
  ],
  ollama: [
    { apiName: "llama3.1", uiName: "Llama 3.1 (local)", supportsTools: true },
    { apiName: "qwen2.5", uiName: "Qwen 2.5 (local)", supportsTools: true },
  ],
};

const PROVIDER_TEMPLATES = [
  {
    providerName: "openai",
    displayName: "OpenAI",
    baseUrl: "https://api.openai.com/v1",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.openai,
  },
  {
    providerName: "anthropic",
    displayName: "Anthropic",
    baseUrl: "https://api.anthropic.com/v1",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.anthropic,
  },
  {
    providerName: "google",
    displayName: "Google AI",
    baseUrl: "https://generativelanguage.googleapis.com/v1beta",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.google,
  },
  {
    providerName: "xai",
    displayName: "xAI",
    baseUrl: "https://api.x.ai/v1",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.xai,
  },
  {
    providerName: "groq",
    displayName: "Groq",
    baseUrl: "https://api.groq.com/openai/v1",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.groq,
  },
  {
    providerName: "ollama",
    displayName: "Ollama (Local)",
    baseUrl: "http://localhost:11434/v1",
    providerType: "openai-compatible" as const,
    models: DEFAULT_MODELS.ollama,
  },
];

export default function ProvidersPage() {
  const [providers, setProviders] = useState<ProviderConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ProviderConfig | null>(
    null
  );
  const [formData, setFormData] = useState<ProviderFormData>({
    providerName: "",
    displayName: "",
    baseUrl: "",
    apiKey: "",
    providerType: "openai-compatible",
    models: [],
  });
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Load providers on mount
  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      const response = await fetch("/api/providers/config");
      if (response.ok) {
        const data = await response.json();
        setProviders(data.providers || []);
      } else {
        toast.error("Failed to load providers");
      }
    } catch (error) {
      console.error("Error loading providers:", error);
      toast.error("Failed to load providers");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      providerName: "",
      displayName: "",
      baseUrl: "",
      apiKey: "",
      providerType: "openai-compatible",
      models: [],
    });
    setEditingProvider(null);
  };

  const openAddDialog = (template?: (typeof PROVIDER_TEMPLATES)[0]) => {
    resetForm();
    if (template) {
      setFormData({
        providerName: template.providerName,
        displayName: template.displayName,
        baseUrl: template.baseUrl,
        apiKey: "",
        providerType: template.providerType,
        models: template.models,
      });
    }
    setDialogOpen(true);
  };

  const openEditDialog = (provider: ProviderConfig) => {
    setEditingProvider(provider);
    setFormData({
      providerName: provider.providerName,
      displayName: provider.displayName,
      baseUrl: provider.baseUrl || "",
      apiKey: "", // Don't pre-fill API key for security
      providerType: provider.providerType,
      models: provider.models,
    });
    setDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!formData.providerName || !formData.displayName) {
      toast.error("Please fill in provider name and display name");
      return;
    }

    setSubmitting(true);
    try {
      const requestData:
        | CreateProviderRequest
        | (UpdateProviderRequest & { id: string }) = {
        providerName: formData.providerName,
        displayName: formData.displayName,
        baseUrl: formData.baseUrl || undefined,
        apiKey: formData.apiKey,
        providerType: formData.providerType,
        models: formData.models,
        ...(editingProvider && { id: editingProvider.id }),
      };

      const url = "/api/providers/config";
      const method = editingProvider ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        toast.success(editingProvider ? "Provider updated" : "Provider added");
        setDialogOpen(false);
        resetForm();
        loadProviders();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to save provider");
      }
    } catch (error) {
      console.error("Error saving provider:", error);
      toast.error("Failed to save provider");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (providerId: string) => {
    if (!confirm("Are you sure you want to delete this provider?")) {
      return;
    }

    try {
      const response = await fetch(`/api/providers/config/${providerId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Provider deleted");
        loadProviders();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete provider");
      }
    } catch (error) {
      console.error("Error deleting provider:", error);
      toast.error("Failed to delete provider");
    }
  };

  const handleTestConnection = async (provider: ProviderConfig) => {
    setTestingProvider(provider.id);
    try {
      const response = await fetch(
        `/api/providers/config/${provider.id}/test`,
        {
          method: "POST",
        }
      );

      const result: TestProviderResponse = await response.json();

      if (result.success) {
        toast.success(
          `Connection successful! Found ${
            result.availableModels?.length || 0
          } models`
        );
      } else {
        toast.error(result.error || "Connection test failed");
      }
    } catch (error) {
      console.error("Error testing provider:", error);
      toast.error("Failed to test connection");
    } finally {
      setTestingProvider(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="size-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Provider Management</h1>
          <p className="text-muted-foreground">
            Configure API keys for AI model providers
          </p>
        </div>
        <Button onClick={() => openAddDialog()}>
          <Plus className="size-4" />
          Add Provider
        </Button>
      </div>

      {/* Provider Templates */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Quick Setup</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {PROVIDER_TEMPLATES.map((template) => {
            const isConfigured = providers.some(
              (p) => p.providerName === template.providerName
            );
            return (
              <Card
                key={template.providerName}
                className="cursor-pointer hover:shadow-md transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">
                      {template.displayName}
                    </CardTitle>
                    {isConfigured ? (
                      <Badge
                        variant="default"
                        className="bg-green-100 text-green-800"
                      >
                        <CheckCircle className="size-3 mr-1" />
                        Configured
                      </Badge>
                    ) : (
                      <Badge variant="secondary">Not configured</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    {template.models.length} models available
                  </p>
                  <Button
                    size="sm"
                    variant={isConfigured ? "outline" : "default"}
                    onClick={() => openAddDialog(template)}
                    className="w-full"
                  >
                    {isConfigured ? "Reconfigure" : "Configure"}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Configured Providers */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Configured Providers</h2>
        {providers.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">
                No providers configured yet. Use the quick setup above to get
                started.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {providers.map((provider) => (
              <Card key={provider.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-base">
                        {provider.displayName}
                      </CardTitle>
                      <CardDescription>{provider.providerName}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {provider.isEnabled ? (
                        <Badge
                          variant="default"
                          className="bg-green-100 text-green-800"
                        >
                          <CheckCircle className="size-3 mr-1" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <XCircle className="size-3 mr-1" />
                          Disabled
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        {provider.models.length} models •{" "}
                        {provider.baseUrl || "Default URL"}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTestConnection(provider)}
                        disabled={testingProvider === provider.id}
                      >
                        {testingProvider === provider.id ? (
                          <Loader className="size-3 animate-spin" />
                        ) : (
                          <TestTube className="size-3" />
                        )}
                        Test
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditDialog(provider)}
                      >
                        <Settings className="size-3" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(provider.id)}
                      >
                        <Trash2 className="size-3" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Add/Edit Provider Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingProvider ? "Edit Provider" : "Add Provider"}
            </DialogTitle>
            <DialogDescription>
              Configure your AI model provider settings
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="provider-name">Provider Name</Label>
                <Input
                  id="provider-name"
                  value={formData.providerName}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      providerName: e.target.value,
                    }))
                  }
                  placeholder="e.g., openai"
                />
              </div>
              <div>
                <Label htmlFor="display-name">Display Name</Label>
                <Input
                  id="display-name"
                  value={formData.displayName}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      displayName: e.target.value,
                    }))
                  }
                  placeholder="e.g., OpenAI"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="base-url">Base URL (optional)</Label>
              <Input
                id="base-url"
                value={formData.baseUrl}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, baseUrl: e.target.value }))
                }
                placeholder="https://api.openai.com/v1"
              />
            </div>

            <div>
              <Label htmlFor="api-key">API Key</Label>
              <Input
                id="api-key"
                type="password"
                value={formData.apiKey}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, apiKey: e.target.value }))
                }
                placeholder="Enter your API key"
              />
            </div>

            <div>
              <Label htmlFor="provider-type">Provider Type</Label>
              <select
                id="provider-type"
                value={formData.providerType}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    providerType: e.target.value as
                      | "openai-compatible"
                      | "azure-openai",
                  }))
                }
                className="w-full h-9 px-3 rounded-md border border-input bg-background text-sm"
              >
                <option value="openai-compatible">OpenAI Compatible</option>
                <option value="azure-openai">Azure OpenAI</option>
              </select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={submitting}>
              {submitting && <Loader className="size-3 animate-spin mr-2" />}
              {editingProvider ? "Update" : "Add"} Provider
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
