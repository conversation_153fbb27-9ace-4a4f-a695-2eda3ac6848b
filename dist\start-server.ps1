# Friday AI Assistant Server Launcher
# This script starts the Next.js server that the desktop application needs

Write-Host "Starting Friday AI Assistant Server..." -ForegroundColor Green
Write-Host ""
Write-Host "This script starts the Next.js server that the desktop application needs."
Write-Host "Keep this window open while using the desktop application."
Write-Host ""

# Change to the project directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir
Set-Location $projectDir

# Check if Node.js is available
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if the standalone server exists
if (-not (Test-Path ".next\standalone\server.js")) {
    Write-Host "ERROR: Next.js standalone server not found" -ForegroundColor Red
    Write-Host "Please run 'pnpm build' first to create the standalone server" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Starting Next.js server on port 3001..." -ForegroundColor Green
Write-Host ""
Write-Host "The server is running. You can now start the desktop application." -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server." -ForegroundColor Yellow
Write-Host ""

# Set environment variables
$env:PORT = "3001"
$env:HOSTNAME = "localhost"
$env:NODE_ENV = "production"

# Start the server
try {
    node .next\standalone\server.js
} catch {
    Write-Host "Error starting server: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
