import { NextRequest, NextResponse } from "next/server";
import { ProviderFileManager } from "@/lib/services/provider-file-manager.service";
import {
  TestProviderResponse,
  ProviderErrorType,
  ProviderConfig,
} from "@/types/provider-config";

export const dynamic = "force-dynamic";

const providerFileManager = new ProviderFileManager();

// Helper to resolve a sensible base URL when not provided
function resolveBaseUrl(provider: ProviderConfig): string {
  if (provider.baseUrl && provider.baseUrl.trim().length > 0)
    return provider.baseUrl;

  const name = provider.providerName.toLowerCase();
  if (name.includes("openai")) return "https://api.openai.com/v1";
  if (name.includes("anthropic")) return "https://api.anthropic.com/v1";
  if (name.includes("groq")) return "https://api.groq.com/openai/v1";
  if (name.includes("xai") || name.includes("grok"))
    return "https://api.x.ai/v1";
  if (name.includes("google"))
    return "https://generativelanguage.googleapis.com/v1beta";
  if (name.includes("ollama")) return "http://localhost:11434/v1";
  return ""; // unknown
}

async function testOpenAICompatible(baseUrl: string, apiKey?: string) {
  // Try GET /models (most OpenAI-compatible providers expose this)
  const url = baseUrl.replace(/\/$/, "") + "/models";
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };
  if (apiKey && apiKey.trim() !== "") {
    headers.Authorization = `Bearer ${apiKey}`;
  }
  const res = await fetch(url, { method: "GET", headers });

  const text = await res.text();
  let availableModels: string[] | undefined;
  try {
    const data = JSON.parse(text);
    // OpenAI-style shape: { data: [{ id: string }] }
    if (Array.isArray(data?.data)) {
      availableModels = data.data.map((m: any) => m?.id).filter(Boolean);
    }
  } catch {
    // ignore parse failure; treat as plain text
  }

  return { ok: res.ok, statusText: res.statusText, availableModels };
}

async function testAnthropic(baseUrl: string, apiKey: string) {
  // Anthropic exposes GET /models with x-api-key and anthropic-version header
  const url = baseUrl.replace(/\/$/, "") + "/models";
  const res = await fetch(url, {
    method: "GET",
    headers: {
      "x-api-key": apiKey,
      "anthropic-version": "2023-06-01",
    },
  });

  const text = await res.text();
  let availableModels: string[] | undefined;
  try {
    const data = JSON.parse(text);
    // Anthropic shape: { data: [{ id: string }] }
    if (Array.isArray(data?.data)) {
      availableModels = data.data.map((m: any) => m?.id).filter(Boolean);
    }
  } catch {}

  return { ok: res.ok, statusText: res.statusText, availableModels };
}

async function testGoogle(baseUrl: string, apiKey: string) {
  // Google Generative Language lists models at /models?key=API_KEY
  const url =
    baseUrl.replace(/\/$/, "") + `/models?key=${encodeURIComponent(apiKey)}`;
  const res = await fetch(url, { method: "GET" });

  const text = await res.text();
  let availableModels: string[] | undefined;
  try {
    const data = JSON.parse(text);
    // Google shape: { models: [{ name: "models/gemini-..." }] }
    if (Array.isArray(data?.models)) {
      availableModels = data.models.map((m: any) => m?.name).filter(Boolean);
    }
  } catch {}

  return { ok: res.ok, statusText: res.statusText, availableModels };
}

export async function POST(
  _req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
): Promise<
  NextResponse<
    TestProviderResponse | { error: string; type?: ProviderErrorType }
  >
> {
  const started = Date.now();
  try {
    const { id } = await ctx.params;
    if (!id) {
      return NextResponse.json(
        {
          error: "Provider ID is required",
          type: ProviderErrorType.VALIDATION_ERROR,
        },
        { status: 400 },
      );
    }

    // Load provider (sans key) and API key
    const provider = await providerFileManager.getProviderById(id);
    if (!provider) {
      return NextResponse.json(
        {
          error: `Provider with ID '${id}' not found`,
          type: ProviderErrorType.VALIDATION_ERROR,
        },
        { status: 404 },
      );
    }
    const apiKey = await providerFileManager.getApiKey(id).catch(() => "");

    const baseUrl = resolveBaseUrl(provider);
    if (!baseUrl) {
      return NextResponse.json(
        {
          error: "Unable to determine provider base URL",
          type: ProviderErrorType.VALIDATION_ERROR,
        },
        { status: 400 },
      );
    }

    let result: {
      ok: boolean;
      statusText: string;
      availableModels?: string[];
    } = {
      ok: false,
      statusText: "",
    };

    const name = provider.providerName.toLowerCase();

    // Provider-specific checks first
    if (name.includes("anthropic")) {
      result = await testAnthropic(baseUrl, apiKey);
    } else if (name.includes("google")) {
      result = await testGoogle(baseUrl, apiKey);
    } else {
      // Default to OpenAI-compatible
      const nameLower = provider.providerName.toLowerCase();
      // For providers like Ollama, omit the Authorization header
      const useKey = nameLower.includes("ollama") ? undefined : apiKey;
      result = await testOpenAICompatible(baseUrl, useKey);

      // If that failed and looks like Anthropic, try fallback
      if (!result.ok && baseUrl.includes("anthropic.com")) {
        result = await testAnthropic(baseUrl, apiKey);
      }
    }

    const latency = Date.now() - started;
    if (result.ok) {
      return NextResponse.json({
        success: true,
        availableModels: result.availableModels,
        latency,
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: result.statusText || "Connection test failed",
        latency,
      },
      { status: 400 },
    );
  } catch (error) {
    const latency = Date.now() - started;
    console.error("Provider test failed:", error);
    return NextResponse.json(
      {
        success: false,
        error: (error as Error).message,
        latency,
      },
      { status: 500 },
    );
  }
}
