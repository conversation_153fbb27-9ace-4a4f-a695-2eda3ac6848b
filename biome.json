{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", ".next", "public", "docker", "dist/**", "*.d.ts"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noUselessTypeConstraint": "error"}, "correctness": {"noUnusedVariables": "error", "useArrayLiterals": "off", "useExhaustiveDependencies": "off"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error"}, "suspicious": {"noExplicitAny": "off", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useNamespaceKeyword": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "overrides": [{"include": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noWith": "off"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}]}