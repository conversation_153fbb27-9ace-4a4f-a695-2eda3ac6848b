#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing standalone build...');

// Copy static files to standalone build
const staticSource = path.join(process.cwd(), '.next/static');
const staticDest = path.join(process.cwd(), '.next/standalone/.next/static');

if (fs.existsSync(staticSource)) {
  console.log('📦 Copying static files...');
  
  // Create destination directory
  fs.mkdirSync(path.dirname(staticDest), { recursive: true });
  
  // Copy static files recursively
  function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    
    if (stats.isDirectory()) {
      fs.mkdirSync(dest, { recursive: true });
      const files = fs.readdirSync(src);
      
      for (const file of files) {
        copyRecursive(path.join(src, file), path.join(dest, file));
      }
    } else {
      fs.copyFileSync(src, dest);
    }
  }
  
  copyRecursive(staticSource, staticDest);
  console.log('✅ Static files copied successfully!');
} else {
  console.log('⚠️ No static files found to copy');
}

// Copy public files to standalone build
const publicSource = path.join(process.cwd(), 'public');
const publicDest = path.join(process.cwd(), '.next/standalone/public');

if (fs.existsSync(publicSource)) {
  console.log('📦 Copying public files...');
  
  function copyRecursive(src, dest) {
    const stats = fs.statSync(src);
    
    if (stats.isDirectory()) {
      fs.mkdirSync(dest, { recursive: true });
      const files = fs.readdirSync(src);
      
      for (const file of files) {
        copyRecursive(path.join(src, file), path.join(dest, file));
      }
    } else {
      fs.copyFileSync(src, dest);
    }
  }
  
  copyRecursive(publicSource, publicDest);
  console.log('✅ Public files copied successfully!');
} else {
  console.log('⚠️ No public files found to copy');
}

console.log('🎉 Standalone build fixed!');