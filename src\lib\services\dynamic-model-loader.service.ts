import { LanguageModel } from "ai";
import { ProviderFileManager } from "./provider-file-manager.service";
import { ProviderConfigEntity } from "@/types/provider-config";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { createAzureOpenAICompatible } from "../ai/azure-openai-compatible";
import { openai } from "@ai-sdk/openai";
import { google } from "@ai-sdk/google";
import { anthropic } from "@ai-sdk/anthropic";
import { xai } from "@ai-sdk/xai";
import logger from "logger";

/**
 * DynamicModelLoader handles loading AI models from user-configured providers
 * with their API keys stored in local configuration
 */
export class DynamicModelLoader {
  private providerFileManager: ProviderFileManager;
  private cachedProviders: Record<string, Record<string, LanguageModel>> = {};
  private cachedUnsupportedModels: Set<LanguageModel> = new Set();
  private lastLoadTime: number = 0;
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.providerFileManager = new ProviderFileManager();
  }

  /**
   * Load all configured providers and their models
   */
  async loadProviders(): Promise<{
    providers: Record<string, Record<string, LanguageModel>>;
    unsupportedModels: Set<LanguageModel>;
  }> {
    try {
      logger.info("[Dynamic Model Loader] Starting to load providers...");

      // Check if cache is still valid
      const now = Date.now();
      if (now - this.lastLoadTime < this.cacheTimeout && Object.keys(this.cachedProviders).length > 0) {
        logger.info("[Dynamic Model Loader] Using cached providers:", Object.keys(this.cachedProviders));
        return {
          providers: this.cachedProviders,
          unsupportedModels: this.cachedUnsupportedModels,
        };
      }

      // Read provider entities with API keys
      const providerEntities = await this.readProviderEntitiesWithKeys();
      logger.info(`[Dynamic Model Loader] Found ${providerEntities.length} provider entities`);

      // Filter only enabled providers
      const enabledProviders = providerEntities.filter(provider => provider.isEnabled);
      logger.info(`[Dynamic Model Loader] Found ${enabledProviders.length} enabled providers:`, enabledProviders.map(p => p.providerName));

      const providers: Record<string, Record<string, LanguageModel>> = {};
      const unsupportedModels = new Set<LanguageModel>();

      for (const provider of enabledProviders) {
        try {
          logger.info(`[Dynamic Model Loader] Creating models for provider: ${provider.providerName}`);
          const { models: providerModels, unsupported } = await this.createProviderModels(provider);

          if (Object.keys(providerModels).length > 0) {
            providers[provider.providerName] = providerModels;
            logger.info(`[Dynamic Model Loader] ✅ Successfully loaded ${Object.keys(providerModels).length} models for ${provider.providerName}`);

            // Add unsupported models to the set
            unsupported.forEach(model => unsupportedModels.add(model));
          } else {
            logger.warn(`[Dynamic Model Loader] ⚠️ No models loaded for provider: ${provider.providerName}`);
          }
        } catch (error) {
          logger.error(`[Dynamic Model Loader] ❌ Failed to load provider ${provider.displayName}:`, error);
          // Continue with other providers even if one fails
        }
      }

      logger.info(`[Dynamic Model Loader] Final result: ${Object.keys(providers).length} providers with models:`, Object.keys(providers));

      // Update cache
      this.cachedProviders = providers;
      this.cachedUnsupportedModels = unsupportedModels;
      this.lastLoadTime = now;

      return { providers, unsupportedModels };
    } catch (error) {
      logger.error("[Dynamic Model Loader] ❌ Failed to load providers:", error);

      // Return cached data if available, otherwise empty
      return {
        providers: this.cachedProviders,
        unsupportedModels: this.cachedUnsupportedModels,
      };
    }
  }

  /**
   * Refresh providers (clear cache and reload)
   */
  async refreshProviders(): Promise<void> {
    this.cachedProviders = {};
    this.cachedUnsupportedModels = new Set();
    this.lastLoadTime = 0;
    await this.loadProviders();
  }

  /**
   * Create models for a specific provider
   */
  private async createProviderModels(provider: ProviderConfigEntity): Promise<{
    models: Record<string, LanguageModel>;
    unsupported: LanguageModel[];
  }> {
    const models: Record<string, LanguageModel> = {};
    const unsupported: LanguageModel[] = [];

    try {
      if (!provider.apiKey || provider.apiKey.trim() === '') {
        logger.warn(`Provider ${provider.displayName} requires an API key`);
        return { models, unsupported };
      }

      // Handle standard AI SDK providers by temporarily setting environment variables
      // But only if they are configured as native providers, not OpenAI-compatible
      if (provider.providerType !== "openai-compatible" && provider.providerType !== "azure-openai") {
        if (provider.providerName === "google") {
          // Temporarily set the Google API key
          const originalKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
          process.env.GOOGLE_GENERATIVE_AI_API_KEY = provider.apiKey;

          provider.models.forEach(({ apiName, uiName, supportsTools }) => {
            try {
              const model = google(apiName);
              models[apiName] = model; // Use apiName as key, not uiName

              if (!supportsTools) {
                unsupported.push(model);
              }
            } catch (error) {
              logger.error(`Failed to create Google model ${uiName}:`, error);
            }
          });

          // Restore original key
          if (originalKey) {
            process.env.GOOGLE_GENERATIVE_AI_API_KEY = originalKey;
          } else {
            delete process.env.GOOGLE_GENERATIVE_AI_API_KEY;
          }
        } else if (provider.providerName === "openai") {
          // Temporarily set the OpenAI API key
          const originalKey = process.env.OPENAI_API_KEY;
          process.env.OPENAI_API_KEY = provider.apiKey;

          provider.models.forEach(({ apiName, uiName, supportsTools }) => {
            try {
              const model = openai(apiName);
              models[apiName] = model; // Use apiName as key, not uiName

              if (!supportsTools) {
                unsupported.push(model);
              }
            } catch (error) {
              logger.error(`Failed to create OpenAI model ${uiName}:`, error);
            }
          });

          // Restore original key
          if (originalKey) {
            process.env.OPENAI_API_KEY = originalKey;
          } else {
            delete process.env.OPENAI_API_KEY;
          }
        } else if (provider.providerName === "anthropic") {
          // Temporarily set the Anthropic API key
          const originalKey = process.env.ANTHROPIC_API_KEY;
          process.env.ANTHROPIC_API_KEY = provider.apiKey;

          provider.models.forEach(({ apiName, uiName, supportsTools }) => {
            try {
              const model = anthropic(apiName);
              models[apiName] = model; // Use apiName as key, not uiName

              if (!supportsTools) {
                unsupported.push(model);
              }
            } catch (error) {
              logger.error(`Failed to create Anthropic model ${uiName}:`, error);
            }
          });

          // Restore original key
          if (originalKey) {
            process.env.ANTHROPIC_API_KEY = originalKey;
          } else {
            delete process.env.ANTHROPIC_API_KEY;
          }
        } else if (provider.providerName === "xai") {
          // Temporarily set the xAI API key
          const originalKey = process.env.XAI_API_KEY;
          process.env.XAI_API_KEY = provider.apiKey;

          provider.models.forEach(({ apiName, uiName, supportsTools }) => {
            try {
              const model = xai(apiName);
              models[apiName] = model; // Use apiName as key, not uiName

              if (!supportsTools) {
                unsupported.push(model);
              }
            } catch (error) {
              logger.error(`Failed to create xAI model ${uiName}:`, error);
            }
          });

          // Restore original key
          if (originalKey) {
            process.env.XAI_API_KEY = originalKey;
          } else {
            delete process.env.XAI_API_KEY;
          }
        }
      } else if (provider.providerType === "azure-openai") {
        // Handle Azure OpenAI with specific requirements
        const azureProvider = createAzureOpenAICompatible({
          name: provider.providerName,
          apiKey: provider.apiKey,
          baseURL: provider.baseUrl || '',
        });

        provider.models.forEach(({ apiName, uiName, supportsTools, apiVersion }) => {
          if (!apiVersion) {
            logger.error(`API version is required for Azure OpenAI model: ${uiName}`);
            return;
          }

          try {
            const model = azureProvider(apiName, apiVersion);
            models[apiName] = model; // Use apiName as key, not uiName

            if (!supportsTools) {
              unsupported.push(model);
            }
          } catch (error) {
            logger.error(`Failed to create Azure OpenAI model ${uiName}:`, error);
          }
        });
      } else {
        // Standard OpenAI-compatible providers
        const baseOptions: { name: string; baseURL: string; apiKey?: string } = {
          name: provider.providerName,
          baseURL: provider.baseUrl || '',
        };
        if (provider.apiKey && provider.apiKey.trim() !== '') {
          baseOptions.apiKey = provider.apiKey;
        }
        const customProvider = createOpenAICompatible(baseOptions);

        provider.models.forEach(({ apiName, uiName, supportsTools }) => {
          try {
            const model = customProvider(apiName);
            models[apiName] = model; // Use apiName as key, not uiName

            if (!supportsTools) {
              unsupported.push(model);
            }
          } catch (error) {
            logger.error(`Failed to create model ${uiName} for provider ${provider.displayName}:`, error);
          }
        });
      }
    } catch (error) {
      logger.error(`Failed to create provider ${provider.displayName}:`, error);
    }

    return { models, unsupported };
  }

  /**
   * Read provider entities with their API keys (internal method)
   */
  private async readProviderEntitiesWithKeys(): Promise<ProviderConfigEntity[]> {
    try {
      // Use the provider file manager's method to read entities
      return await this.providerFileManager['readProviderEntities']();
    } catch (error) {
      logger.error("Failed to read provider entities with keys:", error);
      return [];
    }
  }

  /**
   * Check if a provider has a valid API key
   */
  async hasValidApiKey(providerName: string): Promise<boolean> {
    try {
      const apiKey = await this.providerFileManager.getApiKeyByProviderName(providerName);
      return !!(apiKey && apiKey.trim() !== '');
    } catch (error) {
      return false;
    }
  }

  /**
   * Get available provider names
   */
  async getAvailableProviders(): Promise<string[]> {
    const { providers } = await this.loadProviders();
    return Object.keys(providers);
  }
}