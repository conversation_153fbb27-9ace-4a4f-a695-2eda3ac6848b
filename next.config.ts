import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

// Hybrid architecture: always emit a standalone server for Tauri to run
export default () => {
  const nextConfig: NextConfig = {
    output: "standalone",
    cleanDistDir: true,
    devIndicators: { position: "bottom-right" },
    env: { NO_HTTPS: process.env.NO_HTTPS },
  };
  const withNextIntl = createNextIntlPlugin();
  return withNextIntl(nextConfig);
};
