import {
  ProviderConfig,
  ProviderConfigEntity,
  CreateProviderRequest,
  UpdateProviderRequest,
  ProviderErrorType,
} from "@/types/provider-config";
import {
  createStorageService,
  StorageService,
} from "./storage-factory.service";
import { v4 as uuidv4 } from "uuid";

/**
 * ProviderFileManager handles the business logic for provider configuration management
 * Coordinates between storage, encryption, and validation services
 */
export class ProviderFileManager {
  private storageService: StorageService;

  constructor() {
    this.storageService = createStorageService();
  }

  /**
   * Create a new provider configuration
   */
  async createProvider(config: CreateProviderRequest): Promise<ProviderConfig> {
    try {
      // Validate required fields
      this.validateCreateRequest(config);

      // Read existing providers to check for duplicates
      const existingEntities = await this.readProviderEntities();

      // Check for duplicate provider names
      if (
        existingEntities.some((p) => p.providerName === config.providerName)
      ) {
        throw new Error(
          `Provider with name '${config.providerName}' already exists`,
        );
      }

      // Create new provider entity
      const newProvider: ProviderConfigEntity = {
        id: uuidv4(),
        providerName: config.providerName,
        displayName: config.displayName,
        baseUrl: config.baseUrl,
        apiKey: config.apiKey,
        models: config.models || [],
        isEnabled: true,
        providerType: config.providerType || "openai-compatible",
        metadata: config.metadata || {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Save updated providers
      const updatedProviders = [...existingEntities, newProvider];
      await this.storageService.writeProviders(updatedProviders);

      // Return the created provider
      return this.entityToConfig(newProvider);
    } catch (error) {
      if ((error as Error).message.includes("already exists")) {
        const providerError = new Error((error as Error).message);
        (providerError as any).type = ProviderErrorType.VALIDATION_ERROR;
        throw providerError;
      }

      const providerError = new Error(
        `Failed to create provider: ${(error as Error).message}`,
      );
      (providerError as any).type = ProviderErrorType.FILE_ACCESS_ERROR;
      throw providerError;
    }
  }

  /**
   * Update an existing provider configuration
   */
  async updateProvider(
    id: string,
    updates: UpdateProviderRequest,
  ): Promise<ProviderConfig> {
    try {
      if (!id) {
        throw new Error("Provider ID is required for updates");
      }

      // Read existing providers
      const existingEntities = await this.readProviderEntities();
      const providerIndex = existingEntities.findIndex((p) => p.id === id);

      if (providerIndex === -1) {
        throw new Error(`Provider with ID '${id}' not found`);
      }

      const existingProvider = existingEntities[providerIndex];

      // Prepare updated provider
      const updatedProvider: ProviderConfigEntity = {
        ...existingProvider,
        updatedAt: new Date().toISOString(),
      };

      // Update fields if provided
      if (updates.providerName !== undefined)
        updatedProvider.providerName = updates.providerName;
      if (updates.displayName !== undefined)
        updatedProvider.displayName = updates.displayName;
      if (updates.baseUrl !== undefined)
        updatedProvider.baseUrl = updates.baseUrl;
      if (updates.models !== undefined) updatedProvider.models = updates.models;
      if (updates.isEnabled !== undefined)
        updatedProvider.isEnabled = updates.isEnabled;
      if (updates.providerType !== undefined)
        updatedProvider.providerType = updates.providerType;
      if (updates.metadata !== undefined)
        updatedProvider.metadata = updates.metadata;

      // Update API key if provided
      if (updates.apiKey !== undefined) {
        updatedProvider.apiKey = updates.apiKey;
      }

      // Update the provider in the array
      existingEntities[providerIndex] = updatedProvider;

      // Save updated providers
      await this.storageService.writeProviders(existingEntities);

      // Return the updated provider
      return this.entityToConfig(updatedProvider);
    } catch (error) {
      if ((error as Error).message.includes("not found")) {
        const providerError = new Error((error as Error).message);
        (providerError as any).type = ProviderErrorType.VALIDATION_ERROR;
        throw providerError;
      }

      const providerError = new Error(
        `Failed to update provider: ${(error as Error).message}`,
      );
      (providerError as any).type = ProviderErrorType.FILE_ACCESS_ERROR;
      throw providerError;
    }
  }

  /**
   * Delete a provider configuration
   */
  async deleteProvider(
    id: string,
  ): Promise<{ message: string; deletedProvider: any }> {
    try {
      if (!id) {
        throw new Error("Provider ID is required");
      }

      // Read existing providers
      const existingEntities = await this.readProviderEntities();
      const providerIndex = existingEntities.findIndex((p) => p.id === id);

      if (providerIndex === -1) {
        throw new Error(`Provider with ID '${id}' not found`);
      }

      // Remove the provider
      const deletedProvider = existingEntities[providerIndex];
      existingEntities.splice(providerIndex, 1);

      // Save updated providers
      await this.storageService.writeProviders(existingEntities);

      return {
        message: `Provider '${deletedProvider.displayName}' deleted successfully`,
        deletedProvider: {
          id: deletedProvider.id,
          providerName: deletedProvider.providerName,
          displayName: deletedProvider.displayName,
        },
      };
    } catch (error) {
      if ((error as Error).message.includes("not found")) {
        const providerError = new Error((error as Error).message);
        (providerError as any).type = ProviderErrorType.VALIDATION_ERROR;
        throw providerError;
      }

      const providerError = new Error(
        `Failed to delete provider: ${(error as Error).message}`,
      );
      (providerError as any).type = ProviderErrorType.FILE_ACCESS_ERROR;
      throw providerError;
    }
  }

  /**
   * Get all provider configurations
   */
  async getAllProviders(): Promise<ProviderConfig[]> {
    try {
      return await this.storageService.readProviders();
    } catch (error) {
      const providerError = new Error(
        `Failed to retrieve providers: ${(error as Error).message}`,
      );
      (providerError as any).type = ProviderErrorType.FILE_ACCESS_ERROR;
      throw providerError;
    }
  }

  /**
   * Get a provider configuration by ID
   */
  async getProviderById(id: string): Promise<ProviderConfig | null> {
    try {
      const providers = await this.getAllProviders();
      return providers.find((p) => p.id === id) || null;
    } catch (error) {
      const providerError = new Error(
        `Failed to retrieve provider: ${(error as Error).message}`,
      );
      (providerError as any).type = ProviderErrorType.FILE_ACCESS_ERROR;
      throw providerError;
    }
  }

  /**
   * Get API key for a provider (for internal use)
   */
  async getApiKey(id: string): Promise<string> {
    try {
      const entities = await this.readProviderEntities();
      const provider = entities.find((p) => p.id === id);

      if (!provider) {
        throw new Error(`Provider with ID '${id}' not found`);
      }

      return provider.apiKey;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get API key for a provider by provider name (for internal use)
   */
  async getApiKeyByProviderName(providerName: string): Promise<string> {
    try {
      const entities = await this.readProviderEntities();
      const provider = entities.find((p) => p.providerName === providerName);

      if (!provider) {
        throw new Error(`Provider with name '${providerName}' not found`);
      }

      return provider.apiKey;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate create provider request
   */
  private validateCreateRequest(config: CreateProviderRequest): void {
    if (!config.providerName || !config.displayName) {
      throw new Error(
        "Missing required fields: providerName and displayName are required",
      );
    }

    if (config.providerName.trim().length === 0) {
      throw new Error("Provider name cannot be empty");
    }

    if (config.displayName.trim().length === 0) {
      throw new Error("Display name cannot be empty");
    }

    // API key may be optional for some providers (e.g., local Ollama)
  }

  /**
   * Convert provider entity to config (removes API key for security)
   */
  private entityToConfig(entity: ProviderConfigEntity): ProviderConfig {
    const { apiKey, ...config } = entity;
    return {
      ...config,
      providerType: config.providerType as "openai-compatible" | "azure-openai",
      createdAt: new Date(config.createdAt),
      updatedAt: new Date(config.updatedAt),
    };
  }

  /**
   * Read provider entities with encrypted keys
   */
  private async readProviderEntities(): Promise<ProviderConfigEntity[]> {
    try {
      // Node.js environment - use fs directly (removed Tauri support)
      const configPath = (this.storageService as any).getConfigPath();
      const fs = await import("fs/promises");

      try {
        const data = await fs.readFile(configPath, "utf-8");
        const configFile = JSON.parse(data);
        return configFile.providers || [];
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code === "ENOENT") {
          return [];
        }
        throw error;
      }
    } catch (error) {
      throw new Error(
        `Failed to read provider entities: ${(error as Error).message}`,
      );
    }
  }
}
