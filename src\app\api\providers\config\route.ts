import { NextRequest, NextResponse } from "next/server";
import { ProviderFileManager } from "@/lib/services/provider-file-manager.service";
import {
  CreateProviderRequest,
  UpdateProviderRequest,
  GetProvidersResponse,
  ProviderErrorType,
} from "@/types/provider-config";

export const dynamic = "force-dynamic";

const providerFileManager = new ProviderFileManager();

/**
 * GET /api/providers/config - List all provider configurations
 */
export async function GET(): Promise<
  NextResponse<GetProvidersResponse | { error: string }>
> {
  try {
    const providers = await providerFileManager.getAllProviders();

    return NextResponse.json({
      providers,
    });
  } catch (error) {
    console.error("Failed to get provider configurations:", error);
    return NextResponse.json(
      { error: "Failed to retrieve provider configurations" },
      { status: 500 },
    );
  }
}

/**
 * POST /api/providers/config - Create a new provider configuration
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: CreateProviderRequest = await request.json();

    const newProvider = await providerFileManager.createProvider(body);

    return NextResponse.json(newProvider, { status: 201 });
  } catch (error) {
    console.error("Failed to create provider configuration:", error);

    const errorType =
      (error as any).type || ProviderErrorType.FILE_ACCESS_ERROR;

    let statusCode = 500;
    if (errorType === ProviderErrorType.VALIDATION_ERROR) {
      statusCode = 400;
    } else if ((error as Error).message.includes("already exists")) {
      statusCode = 409;
    }

    return NextResponse.json(
      {
        error: (error as Error).message,
        type: errorType,
      },
      { status: statusCode },
    );
  }
}

/**
 * PUT /api/providers/config - Update an existing provider configuration
 */
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const body: UpdateProviderRequest & { id: string } = await request.json();

    if (!body.id) {
      return NextResponse.json(
        {
          error: "Provider ID is required for updates",
          type: ProviderErrorType.VALIDATION_ERROR,
        },
        { status: 400 },
      );
    }

    const { id, ...updates } = body;
    const updatedProvider = await providerFileManager.updateProvider(
      id,
      updates,
    );

    return NextResponse.json(updatedProvider);
  } catch (error) {
    console.error("Failed to update provider configuration:", error);

    const errorType =
      (error as any).type || ProviderErrorType.FILE_ACCESS_ERROR;

    let statusCode = 500;
    if (errorType === ProviderErrorType.VALIDATION_ERROR) {
      statusCode = (error as Error).message.includes("not found") ? 404 : 400;
    }

    return NextResponse.json(
      {
        error: (error as Error).message,
        type: errorType,
      },
      { status: statusCode },
    );
  }
}
