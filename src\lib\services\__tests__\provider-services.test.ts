import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import { LocalStorageService } from '../local-storage.service';
import { ProviderFileManager } from '../provider-file-manager.service';
import { CreateProviderRequest } from '@/types/provider-config';

describe('Provider Services', () => {
  let tempDir: string;
  let localStorageService: LocalStorageService;
  let providerFileManager: ProviderFileManager;

  beforeEach(async () => {
    // Create a temporary directory for testing
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'provider-test-'));
    
    // Mock the config directory to use our temp directory
    const originalGetConfigDirectory = LocalStorageService.prototype['getConfigDirectory'];
    LocalStorageService.prototype['getConfigDirectory'] = function() {
      return tempDir;
    };

    localStorageService = new LocalStorageService();
    providerFileManager = new ProviderFileManager();
  });

  afterEach(async () => {
    // Clean up temp directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to clean up temp directory:', error);
    }
  });



  describe('LocalStorageService', () => {
    it('should handle empty provider list', async () => {
      const providers = await localStorageService.readProviders();
      expect(providers).toEqual([]);
    });

    it('should create config directory if it does not exist', async () => {
      const configExists = await localStorageService.configExists();
      expect(configExists).toBe(false);
      
      // Writing providers should create the directory
      await localStorageService.writeProviders([]);
      
      const configExistsAfter = await localStorageService.configExists();
      expect(configExistsAfter).toBe(true);
    });
  });

  describe('ProviderFileManager', () => {
    it('should create a new provider', async () => {
      const createRequest: CreateProviderRequest = {
        providerName: 'test-provider',
        displayName: 'Test Provider',
        apiKey: 'test-api-key',
        baseUrl: 'https://api.test.com',
        models: [
          {
            apiName: 'test-model',
            uiName: 'Test Model',
            supportsTools: true,
            contextWindow: 4096,
          }
        ],
        providerType: 'openai-compatible',
        metadata: { test: true },
      };

      const createdProvider = await providerFileManager.createProvider(createRequest);
      
      expect(createdProvider.id).toBeDefined();
      expect(createdProvider.providerName).toBe('test-provider');
      expect(createdProvider.displayName).toBe('Test Provider');
      expect(createdProvider.baseUrl).toBe('https://api.test.com');
      expect(createdProvider.models).toHaveLength(1);
      expect(createdProvider.isEnabled).toBe(true);
      expect(createdProvider.createdAt).toBeInstanceOf(Date);
      expect(createdProvider.updatedAt).toBeInstanceOf(Date);
    });

    it('should retrieve all providers', async () => {
      // Create a provider first
      const createRequest: CreateProviderRequest = {
        providerName: 'test-provider',
        displayName: 'Test Provider',
        apiKey: 'test-api-key',
        models: [],
        providerType: 'openai-compatible',
      };

      await providerFileManager.createProvider(createRequest);
      
      const providers = await providerFileManager.getAllProviders();
      expect(providers).toHaveLength(1);
      expect(providers[0].providerName).toBe('test-provider');
    });

    it('should update a provider', async () => {
      // Create a provider first
      const createRequest: CreateProviderRequest = {
        providerName: 'test-provider',
        displayName: 'Test Provider',
        apiKey: 'test-api-key',
        models: [],
        providerType: 'openai-compatible',
      };

      const createdProvider = await providerFileManager.createProvider(createRequest);
      
      // Update the provider
      const updatedProvider = await providerFileManager.updateProvider(createdProvider.id, {
        displayName: 'Updated Test Provider',
        isEnabled: false,
      });

      expect(updatedProvider.displayName).toBe('Updated Test Provider');
      expect(updatedProvider.isEnabled).toBe(false);
      expect(updatedProvider.updatedAt.getTime()).toBeGreaterThan(updatedProvider.createdAt.getTime());
    });

    it('should delete a provider', async () => {
      // Create a provider first
      const createRequest: CreateProviderRequest = {
        providerName: 'test-provider',
        displayName: 'Test Provider',
        apiKey: 'test-api-key',
        models: [],
        providerType: 'openai-compatible',
      };

      const createdProvider = await providerFileManager.createProvider(createRequest);
      
      // Delete the provider
      const result = await providerFileManager.deleteProvider(createdProvider.id);
      
      expect(result.message).toContain('deleted successfully');
      expect(result.deletedProvider.id).toBe(createdProvider.id);
      
      // Verify it's gone
      const providers = await providerFileManager.getAllProviders();
      expect(providers).toHaveLength(0);
    });

    it('should prevent duplicate provider names', async () => {
      const createRequest: CreateProviderRequest = {
        providerName: 'duplicate-provider',
        displayName: 'Duplicate Provider',
        apiKey: 'test-api-key',
        models: [],
        providerType: 'openai-compatible',
      };

      await providerFileManager.createProvider(createRequest);
      
      // Try to create another with the same name
      await expect(providerFileManager.createProvider(createRequest))
        .rejects.toThrow('already exists');
    });
  });
});