import { pgDb } from "lib/db/pg/db.pg";
import { UserSchema } from "lib/db/pg/schema.pg";
import { eq } from "drizzle-orm";
import { hash } from "bcrypt-ts";
import "load-env";

const DEFAULT_USER = {
  email: "<EMAIL>",
  name: "Desktop User",
  password: "desktop123", // Simple password for local desktop use
};

async function setupDesktopUser() {
  console.log("🔧 Setting up desktop user...");
  
  try {
    // Check if user already exists
    const existingUser = await pgDb
      .select()
      .from(UserSchema)
      .where(eq(UserSchema.email, DEFAULT_USER.email))
      .limit(1);

    if (existingUser.length > 0) {
      console.log("✅ Desktop user already exists");
      return existingUser[0];
    }

    // Create new user
    const hashedPassword = await hash(DEFAULT_USER.password, 10);
    
    const [newUser] = await pgDb
      .insert(UserSchema)
      .values({
        email: DEFAULT_USER.email,
        name: DEFAULT_USER.name,
        password: hashedPassword,
        emailVerified: true, // Auto-verify for desktop use
      })
      .returning();

    console.log("✅ Desktop user created successfully");
    return newUser;
  } catch (error) {
    console.error("❌ Failed to setup desktop user:", error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  setupDesktopUser()
    .then(() => {
      console.log("🎉 Desktop user setup completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Desktop user setup failed:", error);
      process.exit(1);
    });
}

export { setupDesktopUser, DEFAULT_USER };
