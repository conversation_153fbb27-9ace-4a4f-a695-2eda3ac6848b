import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import { ProviderConfig, ProviderConfigFile, ProviderConfigEntity } from '@/types/provider-config';

/**
 * LocalStorageService handles persistent storage of provider configurations
 * in the user's local config directory with automatic backup functionality
 */
export class LocalStorageService {
  private configPath: string;
  private backupPath: string;
  private configDir: string;

  constructor() {
    this.configDir = this.getConfigDirectory();
    this.configPath = path.join(this.configDir, 'providers.json');
    this.backupPath = path.join(this.configDir, 'providers.backup.json');
  }

  /**
   * Get the appropriate config directory based on the platform
   */
  private getConfigDirectory(): string {
    const platform = os.platform();
    const homeDir = os.homedir();

    switch (platform) {
      case 'win32':
        return path.join(process.env.APPDATA || path.join(homeDir, 'AppData', 'Roaming'), 'friday');
      case 'darwin':
        return path.join(homeDir, 'Library', 'Application Support', 'friday');
      default: // Linux and others
        return path.join(process.env.XDG_CONFIG_HOME || path.join(homeDir, '.config'), 'friday');
    }
  }

  /**
   * Ensure the config directory exists
   */
  private async ensureConfigDirectory(): Promise<void> {
    try {
      await fs.access(this.configDir);
    } catch {
      await fs.mkdir(this.configDir, { recursive: true });
      
      // Set restrictive permissions (user only)
      if (os.platform() !== 'win32') {
        await fs.chmod(this.configDir, 0o700);
      }
    }
  }

  /**
   * Read all provider configurations from local storage
   */
  async readProviders(): Promise<ProviderConfig[]> {
    try {
      await this.ensureConfigDirectory();
      
      const data = await fs.readFile(this.configPath, 'utf-8');
      const configFile: ProviderConfigFile = JSON.parse(data);
      
      // Convert stored entities to runtime configs (without API keys for security)
      return configFile.providers.map(entity => {
        const { apiKey, ...config } = entity;
        return {
          ...config,
          createdAt: new Date(config.createdAt),
          updatedAt: new Date(config.updatedAt),
        };
      }) as ProviderConfig[];
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      throw new Error(`Failed to read provider configurations: ${(error as Error).message}`);
    }
  }

  /**
   * Write provider configurations to local storage
   */
  async writeProviders(providers: ProviderConfigEntity[]): Promise<void> {
    try {
      await this.ensureConfigDirectory();
      
      // Create backup before writing
      await this.createBackup();
      
      const configFile: ProviderConfigFile = {
        version: '1.0.0',
        providers,
        settings: {
          autoBackup: true,
          encryptionEnabled: false,
        },
      };
      
      const data = JSON.stringify(configFile, null, 2);
      await fs.writeFile(this.configPath, data, 'utf-8');
      
      // Set restrictive permissions (user only)
      if (os.platform() !== 'win32') {
        await fs.chmod(this.configPath, 0o600);
      }
    } catch (error) {
      throw new Error(`Failed to write provider configurations: ${(error as Error).message}`);
    }
  }

  /**
   * Create a backup of the current configuration
   */
  async createBackup(): Promise<void> {
    try {
      await fs.access(this.configPath);
      await fs.copyFile(this.configPath, this.backupPath);
      
      // Set restrictive permissions on backup
      if (os.platform() !== 'win32') {
        await fs.chmod(this.backupPath, 0o600);
      }
    } catch (error) {
      // If original file doesn't exist, no backup needed
      if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
        console.warn(`Failed to create backup: ${(error as Error).message}`);
      }
    }
  }

  /**
   * Restore configuration from backup
   */
  async restoreFromBackup(): Promise<ProviderConfig[]> {
    try {
      await this.ensureConfigDirectory();
      
      const data = await fs.readFile(this.backupPath, 'utf-8');
      const configFile: ProviderConfigFile = JSON.parse(data);
      
      // Restore the backup as the main config
      await fs.copyFile(this.backupPath, this.configPath);
      
      return configFile.providers.map(entity => {
        const { apiKey, ...config } = entity;
        return {
          ...config,
          createdAt: new Date(config.createdAt),
          updatedAt: new Date(config.updatedAt),
        };
      }) as ProviderConfig[];
    } catch (error) {
      throw new Error(`Failed to restore from backup: ${(error as Error).message}`);
    }
  }

  /**
   * Check if configuration file exists
   */
  async configExists(): Promise<boolean> {
    try {
      await fs.access(this.configPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if backup file exists
   */
  async backupExists(): Promise<boolean> {
    try {
      await fs.access(this.backupPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the config file path (for debugging/testing)
   */
  getConfigPath(): string {
    return this.configPath;
  }

  /**
   * Get the backup file path (for debugging/testing)
   */
  getBackupPath(): string {
    return this.backupPath;
  }
}