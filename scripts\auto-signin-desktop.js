#!/usr/bin/env node

const http = require('http');
const querystring = require('querystring');

const DEFAULT_USER = {
  email: "<EMAIL>",
  password: "desktop123",
};

async function autoSignIn() {
  console.log("🔐 Attempting auto sign-in for desktop user...");
  
  try {
    // Sign in the user
    const signInData = querystring.stringify({
      email: DEFAULT_USER.email,
      password: DEFAULT_USER.password,
    });

    const signInOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/sign-in/email',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(signInData),
      },
    };

    return new Promise((resolve, reject) => {
      const req = http.request(signInOptions, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode === 200 || res.statusCode === 302) {
            console.log("✅ Auto sign-in successful");
            resolve(true);
          } else {
            console.log("⚠️ Auto sign-in failed, status:", res.statusCode);
            resolve(false);
          }
        });
      });

      req.on('error', (error) => {
        console.log("⚠️ Auto sign-in error:", error.message);
        resolve(false);
      });

      req.write(signInData);
      req.end();
    });
  } catch (error) {
    console.log("⚠️ Auto sign-in failed:", error.message);
    return false;
  }
}

// Function to check if server is ready
function checkServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/sign-in',
      method: 'GET',
      timeout: 1000
    }, (res) => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Wait for server and then auto sign-in
async function waitAndSignIn() {
  console.log("⏳ Waiting for server to be ready...");
  
  for (let i = 0; i < 30; i++) {
    const isReady = await checkServer();
    if (isReady) {
      console.log("✅ Server is ready!");
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 more seconds
      return await autoSignIn();
    }
    
    console.log(`   Attempt ${i + 1}/30...`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log("❌ Server failed to start within timeout");
  return false;
}

// Run if called directly
if (require.main === module) {
  waitAndSignIn()
    .then((success) => {
      if (success) {
        console.log("🎉 Auto sign-in completed!");
      } else {
        console.log("⚠️ Auto sign-in failed, manual sign-in required");
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Auto sign-in error:", error);
      process.exit(1);
    });
}

module.exports = { autoSignIn, waitAndSignIn };
