Friday AI Assistant - Desktop Application
==========================================

Version: 1.20.2-FIXED
Build Date: 2025-08-10
Build Type: Release (Production) - Localhost Connection Fixed

Application Details:
- Product Name: Friday AI Assistant
- Identifier: com.friday.ai
- Architecture: x64 (64-bit)
- Framework: Tauri 2.7.0
- Frontend: Next.js 15.3.2
- Runtime: Node.js 20.18.1 (Embedded)

File Information:
- Executable: Friday-AI-Assistant-v1.20.2-FIXED.exe
- Size: ~93MB
- Type: Standalone Desktop Application
- Dependencies: None (Self-contained)

Build Configuration:
- Optimization: Release (Optimized)
- Debug Info: Stripped
- Compression: Enabled
- Bundle Type: Single Executable

System Requirements:
- OS: Windows 10+ (64-bit)
- RAM: 4GB minimum
- Storage: 200MB free space
- Network: Internet connection for AI features

Features Included:
✓ AI Chat Interface
✓ Agent Management
✓ Workflow System
✓ Archive Management
✓ Provider Configuration
✓ MCP Integration
✓ Real-time Features
✓ Embedded Server
✓ Complete UI/UX

Security Features:
✓ Local Data Processing
✓ Secure API Communication
✓ No Telemetry Collection
✓ Privacy-First Design

Build Hash: [Generated during build process]
Tauri Bundle: Complete with all resources
Node.js Bundle: Embedded runtime included
Static Assets: All frontend resources bundled

For support and updates, refer to README.md
