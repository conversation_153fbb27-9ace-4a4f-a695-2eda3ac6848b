import { LocalStorageService } from "./local-storage.service";

/**
 * Factory function to create the appropriate storage service based on the environment
 */
export function createStorageService() {
  // Always use LocalStorageService since we're not using Tauri anymore
  return new LocalStorageService();
}

/**
 * Type for the storage service interface
 */
export type StorageService = LocalStorageService;
