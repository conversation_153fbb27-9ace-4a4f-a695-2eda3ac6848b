import "server-only";

import { createOllama } from "ollama-ai-provider";
import { openai } from "@ai-sdk/openai";
import { google } from "@ai-sdk/google";
import { anthropic } from "@ai-sdk/anthropic";
import { xai } from "@ai-sdk/xai";
import { openrouter } from "@openrouter/ai-sdk-provider";
import { LanguageModel } from "ai";
import {
  createOpenAICompatibleModels,
  openaiCompatibleModelsSafeParse,
} from "./create-openai-compatiable";
import { ChatModel } from "app-types/chat";
import { DynamicModelLoader } from "lib/services/dynamic-model-loader.service";

const ollama = createOllama({
  baseURL: process.env.OLLAMA_BASE_URL || "http://localhost:11434/api",
});

const staticModels = {
  openai: {
    "gpt-4.1": openai("gpt-4.1"),
    "gpt-4.1-mini": openai("gpt-4.1-mini"),
    "4o": openai("gpt-4o"),
    "4o-mini": openai("gpt-4o-mini"),
    "o4-mini": openai("o4-mini", {
      reasoningEffort: "medium",
    }),
  },
  google: {
    "gemini-2.0-flash-lite": google("gemini-2.0-flash-lite"),
    "gemini-2.5-flash": google("gemini-2.5-flash", {}),
    "gemini-2.5-pro": google("gemini-2.5-pro"),
  },
  anthropic: {
    "claude-4-sonnet": anthropic("claude-4-sonnet-20250514"),
    "claude-4-opus": anthropic("claude-4-opus-20250514"),
    "claude-3-7-sonnet": anthropic("claude-3-7-sonnet-latest"),
  },
  xai: {
    "grok-3": xai("grok-3-latest"),
    "grok-3-mini": xai("grok-3-mini-latest"),
  },
  ollama: {
    "gemma3:1b": ollama("gemma3:1b"),
    "gemma3:4b": ollama("gemma3:4b"),
    "gemma3:12b": ollama("gemma3:12b"),
  },
  openRouter: {
    "qwen3-8b:free": openrouter("qwen/qwen3-8b:free"),
    "qwen3-14b:free": openrouter("qwen/qwen3-14b:free"),
    "qwen3-coder": openrouter("qwen/qwen3-coder"),
  },
};

const staticUnsupportedModels = new Set([
  staticModels.openai["o4-mini"],
  staticModels.google["gemini-2.0-flash-lite"],
  staticModels.ollama["gemma3:1b"],
  staticModels.ollama["gemma3:4b"],
  staticModels.ollama["gemma3:12b"],
  staticModels.openRouter["qwen3-8b:free"],
  staticModels.openRouter["qwen3-14b:free"],
]);

const openaiCompatibleProviders = openaiCompatibleModelsSafeParse(
  process.env.OPENAI_COMPATIBLE_DATA,
);

const {
  providers: openaiCompatibleModels,
  unsupportedModels: openaiCompatibleUnsupportedModels,
} = createOpenAICompatibleModels(openaiCompatibleProviders);

const allModels = { ...openaiCompatibleModels, ...staticModels };

const allUnsupportedModels = new Set([
  ...openaiCompatibleUnsupportedModels,
  ...staticUnsupportedModels,
]);

export const isToolCallUnsupportedModel = (model: LanguageModel) => {
  return allUnsupportedModels.has(model);
};

const firstProvider = Object.keys(allModels)[0];
const firstModel = Object.keys(allModels[firstProvider])[0];

const fallbackModel = allModels[firstProvider][firstModel];

export const customModelProvider = {
  modelsInfo: Object.entries(allModels).map(([provider, models]) => ({
    provider,
    models: Object.entries(models).map(([name, model]) => ({
      name,
      isToolCallUnsupported: isToolCallUnsupportedModel(model),
    })),
  })),
  getModel: async (model?: ChatModel): Promise<LanguageModel> => {
    if (!model) {
      console.log(`[Model Loading] No model provided, using fallback`);
      return fallbackModel;
    }

    try {
      console.log(`[Model Loading] 🚀 Attempting to load model: ${model.provider}/${model.model}`);

      // Always try to get model from user-configured providers first
      const dynamicLoader = new DynamicModelLoader();
      console.log(`[Model Loading] 📂 Loading dynamic providers...`);
      const { providers } = await dynamicLoader.loadProviders();

      console.log(`[Model Loading] 📋 Available dynamic providers:`, Object.keys(providers));
      console.log(`[Model Loading] 🔍 Looking for provider: ${model.provider}`);

      if (providers[model.provider]) {
        console.log(`[Model Loading] 📦 Provider ${model.provider} found with models:`, Object.keys(providers[model.provider]));
      }

      // Check if this provider is configured by the user
      const dynamicModel = providers[model.provider]?.[model.model];
      if (dynamicModel) {
        console.log(`[Model Loading] ✅ SUCCESS: Using user-configured model: ${model.provider}/${model.model}`);
        return dynamicModel;
      }

      console.log(`[Model Loading] ❌ No user-configured model found for ${model.provider}/${model.model}`);

      // Only fallback to static models if the provider is not user-configured
      // and has environment variables set
      const staticModel = allModels[model.provider]?.[model.model];
      if (staticModel) {
        console.log(`[Model Loading] 🔄 FALLBACK: Using static model: ${model.provider}/${model.model}`);
        return staticModel;
      }

      console.warn(`[Model Loading] ⚠️ Model not found: ${model.provider}/${model.model}, using fallback`);
      return fallbackModel;
    } catch (error) {
      console.error('[Model Loading] ❌ ERROR loading model:', error);
      // Fallback to static models
      return allModels[model.provider]?.[model.model] || fallbackModel;
    }
  },
};
