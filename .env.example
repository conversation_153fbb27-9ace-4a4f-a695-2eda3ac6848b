# === LLM Provider API Keys ===
# You only need to enter the keys for the providers you plan to use
GOOGLE_GENERATIVE_AI_API_KEY=****
OPENAI_API_KEY=****
XAI_API_KEY=****
ANTHROPIC_API_KEY=****
OPENROUTER_API_KEY=****
OLLAMA_BASE_URL=http://localhost:11434/api


# === Database ===
# If you don't have PostgreSQL running locally, start it with: pnpm docker:pg
POSTGRES_URL=postgres://your_username:your_password@localhost:5432/your_database_name

# Secret for Better Auth (generate with: npx @better-auth/cli@latest secret)
BETTER_AUTH_SECRET=****

# (Optional)
# URL for Better Auth (the URL you access the app from)
# IMPORTANT: Set this to https://localhost:3000 if using HTTPS locally
# For production, this should match your domain (e.g., https://yourdomain.com)
BETTER_AUTH_URL=

# (Optional)
# === Tools === 
# Exa AI for web search and content extraction (optional, but recommended for @web and research features)
EXA_API_KEY=



# ========================================================================
# === OPTIONAL SETTINGS BELOW (not required for basic functionality) ===
# ========================================================================


# (Optional) Redis for Multi-Instance Support
# When running multiple server instances (load balancing, clustering):
# - With Redis: Real-time MCP synchronization + reduced polling
# - Without Redis: Polling-only synchronization (single instance or dev mode)
# redis://localhost:6379
REDIS_URL=


# (Optional)
# Whether to use file-based MCP config (default: false)
FILE_BASED_MCP_CONFIG=false

# (Optional) 
# === OAuth Settings ===
# Fill in these values only if you want to enable Google/GitHub/Microsoft login

#GitHub
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

#Google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
# Set to 1 to force account selection
GOOGLE_FORCE_ACCOUNT_SELECTION=


# Microsoft
MICROSOFT_CLIENT_ID=
MICROSOFT_CLIENT_SECRET=
# Optional Tenant Id
MICROSOFT_TENANT_ID=
# Set to 1 to force account selection
MICROSOFT_FORCE_ACCOUNT_SELECTION=

# (Optional)
# Set this to 1 to disable email sign in
DISABLE_EMAIL_SIGN_IN=

# (Optional)
# Set this to 1 to disable user sign-ups.
DISABLE_SIGN_UP=

# (Optional)
# Set this to 1 to disallow adding MCP servers.
NOT_ALLOW_ADD_MCP_SERVERS=

# (Optional)
# Maximum timeout for MCP tool calls in milliseconds (default: no timeout)
# Useful for long-running MCP tools. Example: 600000 (10 minutes)
MCP_MAX_TOTAL_TIMEOUT=
