@echo off
echo Starting Friday AI Assistant Server...
echo.
echo This script starts the Next.js server that the desktop application needs.
echo Keep this window open while using the desktop application.
echo.

REM Change to the project directory (adjust path as needed)
cd /d "%~dp0\.."

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check if the standalone server exists
if not exist ".next\standalone\server.js" (
    echo ERROR: Next.js standalone server not found
    echo Please run 'pnpm build' first to create the standalone server
    echo.
    pause
    exit /b 1
)

echo Starting Next.js server on port 3001...
echo.
echo The server is running. You can now start the desktop application.
echo Press Ctrl+C to stop the server.
echo.

REM Set environment variables and start the server
set PORT=3001
set HOSTNAME=localhost
set NODE_ENV=production

REM Start the server
node .next\standalone\server.js
