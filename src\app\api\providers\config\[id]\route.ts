import { NextRequest, NextResponse } from "next/server";
import { ProviderFileManager } from "@/lib/services/provider-file-manager.service";
import { ProviderErrorType } from "@/types/provider-config";

export const dynamic = "force-dynamic";

const providerFileManager = new ProviderFileManager();

/**
 * DELETE /api/providers/config/[id] - Delete a provider configuration
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        {
          error: "Provider ID is required",
          type: ProviderErrorType.VALIDATION_ERROR,
        },
        { status: 400 },
      );
    }

    const result = await providerFileManager.deleteProvider(id);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Failed to delete provider configuration:", error);

    const errorType =
      (error as any).type || ProviderErrorType.FILE_ACCESS_ERROR;

    let statusCode = 500;
    if (errorType === ProviderErrorType.VALIDATION_ERROR) {
      statusCode = (error as Error).message.includes("not found") ? 404 : 400;
    }

    return NextResponse.json(
      {
        error: (error as Error).message,
        type: errorType,
      },
      { status: statusCode },
    );
  }
}
