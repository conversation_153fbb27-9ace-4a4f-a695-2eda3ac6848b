import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DynamicModelLoader } from '../dynamic-model-loader.service';
import { ProviderFileManager } from '../provider-file-manager.service';

// Mock the provider file manager
vi.mock('../provider-file-manager.service');

describe('DynamicModelLoader', () => {
  let dynamicModelLoader: DynamicModelLoader;
  let mockProviderFileManager: any;

  beforeEach(() => {
    vi.clearAllMocks();
    dynamicModelLoader = new DynamicModelLoader();
    mockProviderFileManager = vi.mocked(ProviderFileManager);
  });

  it('should load providers successfully', async () => {
    // Mock the private method to return test data
    const mockProviderEntities = [
      {
        id: 'test-1',
        providerName: 'test-provider',
        displayName: 'Test Provider',
        baseUrl: 'https://api.test.com/v1',
        apiKey: 'test-key',
        models: [
          {
            apiName: 'test-model',
            uiName: 'Test Model',
            supportsTools: true,
          },
        ],
        isEnabled: true,
        providerType: 'openai-compatible',
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Mock the file system read
    vi.doMock('fs/promises', () => ({
      readFile: vi.fn().mockResolvedValue(JSON.stringify({
        providers: mockProviderEntities,
      })),
    }));

    const result = await dynamicModelLoader.loadProviders();

    expect(result.providers).toBeDefined();
    expect(result.unsupportedModels).toBeDefined();
  });

  it('should handle empty provider list', async () => {
    // Mock empty providers
    vi.doMock('fs/promises', () => ({
      readFile: vi.fn().mockRejectedValue({ code: 'ENOENT' }),
    }));

    const result = await dynamicModelLoader.loadProviders();

    expect(result.providers).toEqual({});
    expect(result.unsupportedModels.size).toBe(0);
  });

  it('should filter disabled providers', async () => {
    const mockProviderEntities = [
      {
        id: 'test-1',
        providerName: 'enabled-provider',
        displayName: 'Enabled Provider',
        baseUrl: 'https://api.enabled.com/v1',
        apiKey: 'test-key',
        models: [],
        isEnabled: true,
        providerType: 'openai-compatible',
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'test-2',
        providerName: 'disabled-provider',
        displayName: 'Disabled Provider',
        baseUrl: 'https://api.disabled.com/v1',
        apiKey: 'test-key',
        models: [],
        isEnabled: false,
        providerType: 'openai-compatible',
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Mock the file system read
    vi.doMock('fs/promises', () => ({
      readFile: vi.fn().mockResolvedValue(JSON.stringify({
        providers: mockProviderEntities,
      })),
    }));

    const result = await dynamicModelLoader.loadProviders();

    // Should only include enabled providers
    expect(Object.keys(result.providers)).not.toContain('Disabled Provider');
  });

  it('should handle providers without API keys', async () => {
    const mockProviderEntities = [
      {
        id: 'test-1',
        providerName: 'no-key-provider',
        displayName: 'No Key Provider',
        baseUrl: 'https://api.nokey.com/v1',
        apiKey: '', // Empty API key
        models: [
          {
            apiName: 'test-model',
            uiName: 'Test Model',
            supportsTools: true,
          },
        ],
        isEnabled: true,
        providerType: 'openai-compatible',
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Mock the file system read
    vi.doMock('fs/promises', () => ({
      readFile: vi.fn().mockResolvedValue(JSON.stringify({
        providers: mockProviderEntities,
      })),
    }));

    const result = await dynamicModelLoader.loadProviders();

    // Should not include providers without API keys
    expect(Object.keys(result.providers)).not.toContain('No Key Provider');
  });
});