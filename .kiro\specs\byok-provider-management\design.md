# Design Document

## Overview

The BYOK (Bring Your Own Key) provider management system will transform the current file-based provider configuration into a comprehensive UI-driven solution integrated into the desktop application. The system will provide a seamless interface for users to manage AI model providers, their configurations, and API keys while maintaining backward compatibility with the existing system.

The design leverages the existing Next.js app router structure and Electron desktop framework to create a native desktop experience for provider management using local file storage.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[Provider Management UI] --> API[Provider Management API]
    API --> FileSystem[Local File Storage]
    API --> Crypto[Encryption Service]
    API --> Validator[Provider Validator]
    API --> ModelLoader[Dynamic Model Loader]

    ModelLoader --> StaticModels[Static Models]
    ModelLoader --> DynamicModels[Dynamic Provider Models]

    UI --> Sidebar[App Sidebar Navigation]
    UI --> ProviderForm[Provider Configuration Form]
    UI --> TestConnection[Connection Testing]

    subgraph "Data Flow"
        Config[Provider Config] --> Encrypt[Encrypt API Keys]
        Encrypt --> Store[Store in Local File]
        Store --> Load[Load on Startup]
        Load --> Decrypt[Decrypt API Keys]
        Decrypt --> Initialize[Initialize Models]
    end

    subgraph "Local Storage"
        FileSystem --> ConfigFile[providers.json]
        FileSystem --> BackupFile[providers.backup.json]
        FileSystem --> LegacyFile[openai-compatible.config.ts]
    end
```

### Local File Storage Structure

The system will use local JSON files for storing provider configurations in the user's application data directory:

```
~/.config/friday/          # Linux/macOS
%APPDATA%/friday/          # Windows
├── providers.json                 # Main provider configurations
├── providers.backup.json          # Automatic backup
└── app-settings.json             # General app settings
```

**providers.json Structure:**

```json
{
  "version": "1.0.0",
  "providers": [
    {
      "id": "uuid-string",
      "providerName": "groq",
      "displayName": "Groq",
      "baseUrl": "https://api.groq.com/openai/v1",
      "encryptedApiKey": "encrypted-key-string",
      "models": [...],
      "isEnabled": true,
      "providerType": "openai-compatible",
      "metadata": {},
      "createdAt": "2025-01-07T...",
      "updatedAt": "2025-01-07T..."
    }
  ],
  "settings": {
    "autoBackup": true,
    "encryptionEnabled": true
  }
}
```

### Security Architecture

- **API Key Encryption**: All API keys will be encrypted using AES-256-GCM before local storage
- **Key Derivation**: Machine-specific encryption keys derived from hardware identifiers
- **Secure Storage**: Encrypted data stored in local JSON files with restricted file permissions
- **Memory Protection**: API keys decrypted only when needed and cleared from memory after use
- **File Permissions**: Configuration files set to user-only read/write (600 permissions)

## Components and Interfaces

### 1. Provider Management UI Components

#### ProviderManagementPage (`/providers`)

- Main interface for managing all providers
- List view of configured providers with status indicators
- Add/Edit/Delete actions
- Import/Export functionality

#### ProviderConfigurationDialog

- Modal dialog for adding/editing provider configurations
- Form validation and real-time feedback
- Connection testing integration
- Template selection for popular providers

#### ProviderTestConnection

- Component for testing provider connectivity
- Real-time status updates during testing
- Detailed error reporting and troubleshooting

#### ProviderTemplateSelector

- Pre-configured templates for popular providers (OpenAI, Anthropic, Groq, Azure OpenAI)
- Quick setup with minimal configuration required

### 2. API Layer

#### Provider Management API (`/api/providers`)

```typescript
// GET /api/providers - List all providers from local storage
interface GetProvidersResponse {
  providers: ProviderConfig[];
}

// POST /api/providers - Create new provider in local storage
interface CreateProviderRequest {
  providerName: string;
  displayName: string;
  baseUrl?: string;
  apiKey: string;
  models: ModelConfig[];
  providerType: "openai-compatible" | "azure-openai";
  metadata?: Record<string, any>;
}

// PUT /api/providers/:id - Update provider in local storage
interface UpdateProviderRequest extends Partial<CreateProviderRequest> {
  isEnabled?: boolean;
}

// DELETE /api/providers/:id - Delete provider from local storage

// POST /api/providers/:id/test - Test provider connection
interface TestProviderResponse {
  success: boolean;
  availableModels?: string[];
  error?: string;
  latency?: number;
}

// POST /api/providers/import - Import from legacy config file
interface ImportProvidersRequest {
  source: "file-config" | "json-file";
  data?: string; // JSON string for json-file import
}

// GET /api/providers/export - Export providers to JSON
interface ExportProvidersResponse {
  data: string; // JSON string
  filename: string;
}
```

#### Provider Configuration Types

```typescript
interface ProviderConfig {
  id: string;
  providerName: string;
  displayName: string;
  baseUrl?: string;
  models: ModelConfig[];
  isEnabled: boolean;
  providerType: "openai-compatible" | "azure-openai";
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  status?: "active" | "error" | "testing";
}

interface ModelConfig {
  apiName: string;
  uiName: string;
  supportsTools: boolean;
  apiVersion?: string; // Required for Azure OpenAI
  contextWindow?: number;
  maxTokens?: number;
}
```

### 3. Service Layer

#### LocalStorageService

```typescript
class LocalStorageService {
  private configPath: string;
  private backupPath: string;

  async readProviders(): Promise<ProviderConfig[]>;
  async writeProviders(providers: ProviderConfig[]): Promise<void>;
  async createBackup(): Promise<void>;
  async restoreFromBackup(): Promise<ProviderConfig[]>;
  private ensureConfigDirectory(): Promise<void>;
  private getConfigPath(): string;
}
```

#### ProviderEncryptionService

```typescript
class ProviderEncryptionService {
  async encryptApiKey(apiKey: string): Promise<string>;
  async decryptApiKey(encryptedKey: string): Promise<string>;
  private getMachineKey(): Promise<Buffer>;
  private deriveEncryptionKey(): Promise<Buffer>;
}
```

#### ProviderValidationService

```typescript
class ProviderValidationService {
  async validateProvider(config: ProviderConfig): Promise<ValidationResult>;
  async testConnection(config: ProviderConfig): Promise<TestResult>;
  private validateOpenAICompatible(config: ProviderConfig): ValidationResult;
  private validateAzureOpenAI(config: ProviderConfig): ValidationResult;
}
```

#### DynamicModelLoader

```typescript
class DynamicModelLoader {
  async loadProviders(): Promise<ProviderModels>;
  async refreshProviders(): Promise<void>;
  private createProviderModels(configs: ProviderConfig[]): ProviderModels;
  private watchConfigFile(): void;
}
```

### 4. File System Layer

#### ProviderFileManager

```typescript
class ProviderFileManager {
  async createProvider(config: CreateProviderRequest): Promise<ProviderConfig>;
  async updateProvider(
    id: string,
    updates: UpdateProviderRequest
  ): Promise<ProviderConfig>;
  async deleteProvider(id: string): Promise<void>;
  async getAllProviders(): Promise<ProviderConfig[]>;
  async getProviderById(id: string): Promise<ProviderConfig | null>;
  async importFromLegacyConfig(): Promise<ProviderConfig[]>;
  async exportToFile(): Promise<string>;
}
```

## Data Models

### Provider Configuration Storage

```typescript
// File storage format
interface ProviderConfigFile {
  version: string;
  providers: ProviderConfigEntity[];
  settings: {
    autoBackup: boolean;
    encryptionEnabled: boolean;
  };
}

// Individual provider entity
interface ProviderConfigEntity {
  id: string;
  providerName: string;
  displayName: string;
  baseUrl?: string;
  encryptedApiKey: string;
  models: ModelConfig[];
  isEnabled: boolean;
  providerType: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Runtime model (with decrypted key)
interface RuntimeProviderConfig
  extends Omit<ProviderConfigEntity, "encryptedApiKey"> {
  apiKey: string;
}
```

### Migration Strategy

The system will support migration from the existing file-based configuration:

```typescript
interface MigrationService {
  migrateFromFileConfig(): Promise<MigrationResult>;
  exportToFileConfig(): Promise<string>;
  private parseExistingConfig(): OpenAICompatibleProvider[];
  private detectLegacyConfig(): boolean;
}
```

## Error Handling

### Error Types and Responses

```typescript
enum ProviderErrorType {
  INVALID_API_KEY = "invalid_api_key",
  NETWORK_ERROR = "network_error",
  INVALID_BASE_URL = "invalid_base_url",
  RATE_LIMITED = "rate_limited",
  PROVIDER_UNAVAILABLE = "provider_unavailable",
  ENCRYPTION_ERROR = "encryption_error",
  VALIDATION_ERROR = "validation_error",
  FILE_ACCESS_ERROR = "file_access_error",
  BACKUP_ERROR = "backup_error",
}

interface ProviderError {
  type: ProviderErrorType;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}
```

### Error Handling Strategy

1. **File System Errors**: Handle permission issues, disk space, and file corruption
2. **API Errors**: Structured error responses with specific error types
3. **UI Feedback**: Toast notifications and inline error messages
4. **Graceful Degradation**: Fallback to working providers when one fails
5. **Retry Logic**: Automatic retry for transient errors
6. **User Guidance**: Helpful error messages with troubleshooting steps
7. **Backup Recovery**: Automatic backup creation and recovery options

## Testing Strategy

### Unit Testing

- **Service Layer**: Test encryption, validation, and model loading services
- **File Manager**: Test file operations with mock file system
- **API Layer**: Test all endpoints with various scenarios
- **Utility Functions**: Test helper functions and validators

### Integration Testing

- **File System Integration**: Test with real file system operations
- **API Integration**: Test full request/response cycles
- **Provider Integration**: Test with mock AI provider endpoints
- **Encryption Integration**: Test encryption/decryption workflows

### End-to-End Testing

- **User Workflows**: Test complete provider management workflows
- **UI Interactions**: Test form submissions, validations, and feedback
- **Error Scenarios**: Test error handling and recovery
- **Migration Testing**: Test migration from file-based configuration

### Security Testing

- **Encryption Validation**: Verify API keys are properly encrypted
- **File Permissions**: Test file access controls and permissions
- **Input Validation**: Test against injection attacks and malformed data
- **Key Management**: Test key derivation and secure storage

## Implementation Phases

### Phase 1: Core Infrastructure

- Local file storage service
- Encryption service implementation
- Basic API endpoints
- Provider file manager

### Phase 2: UI Components

- Provider management page
- Configuration forms
- Connection testing
- Basic CRUD operations

### Phase 3: Advanced Features

- Provider templates
- Import/export functionality
- Migration from file-based config
- Enhanced error handling

### Phase 4: Polish and Optimization

- Performance optimizations
- Enhanced UI/UX
- Comprehensive testing
- Documentation and help system

## Navigation Integration

The provider management interface will be integrated into the existing sidebar navigation:

```typescript
// Addition to app-sidebar-menus.tsx
<SidebarMenuItem>
  <Link href="/providers">
    <SidebarMenuButton className="font-semibold">
      <KeyIcon className="size-4" />
      {t("Layout.providerManagement")}
    </SidebarMenuButton>
  </Link>
</SidebarMenuItem>
```

## Backward Compatibility

The system will maintain backward compatibility with the existing file-based configuration:

1. **Automatic Migration**: Detect existing `openai-compatible.config.ts` and offer migration
2. **Dual Support**: Support both local JSON and legacy file configurations during transition
3. **Export Capability**: Allow exporting local configuration back to legacy file format
4. **Graceful Fallback**: Fall back to legacy config if local storage is unavailable

## Performance Considerations

- **Lazy Loading**: Load provider configurations only when needed
- **File Watching**: Watch configuration file for external changes
- **Caching**: Cache decrypted configurations in memory with TTL
- **Batch Operations**: Support bulk operations for multiple providers
- **Background Refresh**: Periodically refresh provider status in background
- **Atomic Writes**: Use atomic file operations to prevent corruption

## Platform-Specific Considerations

### Windows

- Store configuration in `%APPDATA%/friday/`
- Handle Windows file locking and permissions
- Support Windows-specific encryption APIs

### macOS

- Store configuration in `~/Library/Application Support/friday/`
- Use macOS Keychain for additional security (optional)
- Handle macOS file permissions and sandboxing

### Linux

- Store configuration in `~/.config/friday/`
- Follow XDG Base Directory specification
- Handle various Linux file systems and permissions
