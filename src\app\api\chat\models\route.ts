import { customModelProvider } from "lib/ai/models";
import { DynamicModelLoader } from "lib/services/dynamic-model-loader.service";

export const dynamic = "force-dynamic";

export const GET = async () => {
  try {
    const dynamicLoader = new DynamicModelLoader();

    // Get static models info as base
    const staticModelsInfo = customModelProvider.modelsInfo;

    // Filter to only show providers that have valid API keys
    const configuredProviders: typeof staticModelsInfo = [];

    for (const providerInfo of staticModelsInfo) {
      const hasValidKey = await dynamicLoader.hasValidApiKey(providerInfo.provider);
      if (hasValidKey) {
        configuredProviders.push(providerInfo);
      }
    }

    // If no providers are configured, return empty array
    if (configuredProviders.length === 0) {
      return Response.json([]);
    }

    return Response.json(configuredProviders);
  } catch (error) {
    console.error('Error loading configured models:', error);
    // Fallback to static models if there's an error
    return Response.json(customModelProvider.modelsInfo);
  }
};
