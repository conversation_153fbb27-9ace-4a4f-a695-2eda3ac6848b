#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import http from 'http';

const PORT = 3000;
const APP_NAME = 'Friday AI';
const OUTPUT_DIR = 'desktop-build';

console.log('🚀 Building desktop application...');

// Function to check if server is running
function checkServer(port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 1000
    }, (res) => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Function to wait for server to be ready
async function waitForServer(port, maxAttempts = 30) {
  console.log(`⏳ Waiting for server on port ${port}...`);
  
  for (let i = 0; i < maxAttempts; i++) {
    const isRunning = await checkServer(port);
    if (isRunning) {
      console.log('✅ Server is ready!');
      return true;
    }
    
    console.log(`   Attempt ${i + 1}/${maxAttempts}...`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  throw new Error('Server failed to start within timeout period');
}

// Function to build and start the production server
async function buildAndStartProductionServer() {
  console.log('📦 Building production version...');

  // Build the production version
  await new Promise((resolve, reject) => {
    const buildProcess = spawn('pnpm', ['build'], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Production build completed!');
        resolve();
      } else {
        reject(new Error(`Build failed with code ${code}`));
      }
    });
  });

  // Fix standalone build
  await new Promise((resolve, reject) => {
    const fixProcess = spawn('node', ['scripts/fix-standalone.cjs'], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    fixProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Standalone build fixed!');
        resolve();
      } else {
        reject(new Error(`Fix standalone failed with code ${code}`));
      }
    });
  });

  console.log('🖥️ Starting production server...');

  const serverProcess = spawn('pnpm', ['start'], {
    stdio: 'pipe',
    shell: true,
    cwd: process.cwd(),
    env: { ...process.env, PORT: PORT }
  });

  serverProcess.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('Ready in') || output.includes('Local:')) {
      console.log('✅ Production server started!');
    }
  });

  serverProcess.stderr.on('data', (data) => {
    console.error('Server error:', data.toString());
  });

  return serverProcess;
}

// Function to create desktop app with nativefier
async function createDesktopApp() {
  console.log('🖥️ Creating desktop application...');
  
  const logoPath = path.join(process.cwd(), 'public', 'logo.png');
  const outputPath = path.join(process.cwd(), OUTPUT_DIR);
  
  // Clean output directory
  if (fs.existsSync(outputPath)) {
    fs.rmSync(outputPath, { recursive: true, force: true });
  }
  
  const nativefierArgs = [
    `http://localhost:${PORT}`,
    outputPath,
    '--name', APP_NAME,
    '--platform', 'windows',
    '--arch', 'x64',
    '--electron-version', '31.0.0',
    '--single-instance',
    '--disable-dev-tools',
    '--hide-window-frame',
    '--maximize',
    '--show-menu-bar',
    '--internal-urls', '.*',
    '--file-download-options', '{"saveAs": true}',
    '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  ];
  
  // Add icon if logo exists
  if (fs.existsSync(logoPath)) {
    nativefierArgs.push('--icon', logoPath);
    console.log('🎨 Using logo.png as application icon');
  }
  
  return new Promise((resolve, reject) => {
    const nativefierProcess = spawn('npx', ['nativefier', ...nativefierArgs], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });
    
    nativefierProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Desktop application created successfully!');
        console.log(`📁 Output directory: ${outputPath}`);
        resolve();
      } else {
        reject(new Error(`Nativefier failed with code ${code}`));
      }
    });
  });
}

// Main function
async function main() {
  let serverProcess = null;
  
  try {
    // Build and start production server
    serverProcess = await buildAndStartProductionServer();
    
    // Wait for server to be ready
    await waitForServer(PORT);
    
    // Create desktop app
    await createDesktopApp();
    
    console.log('🎉 Desktop application build completed!');
    console.log(`📂 Find your executable in: ${path.join(process.cwd(), OUTPUT_DIR)}`);
    
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  } finally {
    // Clean up server process
    if (serverProcess) {
      console.log('🧹 Cleaning up server process...');
      serverProcess.kill();
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Build interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Build terminated');
  process.exit(0);
});

main();
