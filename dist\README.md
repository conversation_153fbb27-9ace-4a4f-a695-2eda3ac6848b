# Friday AI Assistant - Desktop Application

## Version 1.20.1

Welcome to Friday AI Assistant, your intelligent desktop companion built with cutting-edge AI technology.

## 🚀 Quick Start

### Option 1: Simple Launch (Recommended)

1. **Download**: Get the latest version from this distribution package
2. **Start Server**: Double-click `start-server.bat` (or `start-server.ps1` for PowerShell)
3. **Start App**: Double-click `Friday-AI-Assistant-v1.20.2-FIXED.exe`
4. **Enjoy**: The application will connect to the local server automatically!

### Option 2: Manual Setup

1. **Install Node.js**: Download from https://nodejs.org/ (if not already installed)
2. **Start Server**: Run the server launcher script first
3. **Start App**: Launch the desktop application
4. **Keep Both Running**: Keep the server window open while using the app

## 📋 System Requirements

- **Operating System**: Windows 10 or later (64-bit)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 200MB free disk space
- **Network**: Internet connection required for AI features

## ✨ Features

- **AI-Powered Chat**: Advanced conversational AI capabilities
- **Agent Management**: Create and manage custom AI agents
- **Workflow Automation**: Build and execute automated workflows
- **Archive System**: Organize and search your conversations
- **Provider Configuration**: Support for multiple AI providers
- **MCP Integration**: Model Context Protocol support
- **Real-time Communication**: WebSocket-based real-time features

## 🔧 Technical Details

- **Framework**: Built with Tauri (Rust + Web Technologies)
- **Frontend**: Next.js 15 with React
- **Backend**: Embedded Node.js server
- **Architecture**: Hybrid desktop application
- **Size**: ~93MB (includes all dependencies)

## 🛡️ Security & Privacy

- **Local Processing**: Core application runs entirely on your machine
- **Data Privacy**: Your conversations and data remain on your device
- **Secure Communication**: All external API calls use secure protocols
- **No Telemetry**: No usage data is collected or transmitted

## 📁 Application Structure

The application is completely self-contained:

- ✅ Embedded Node.js runtime
- ✅ Complete Next.js application
- ✅ All static assets and resources
- ✅ Application icons and metadata

## 🚨 Troubleshooting

### "Can't reach this page" or "localhost refused to connect"

- **Solution**: Start the server first using `start-server.bat` before launching the app
- **Check**: Ensure Node.js is installed (download from https://nodejs.org/)
- **Verify**: The server script should show "Server running on port 3001"
- **Keep Running**: Don't close the server window while using the app

### Application Won't Start

- Ensure you have Windows 10 or later
- Check that you have sufficient disk space
- Try running as administrator if needed
- Make sure the server is running first

### Server Won't Start

- Install Node.js from https://nodejs.org/
- Ensure you have the complete project files
- Check that port 3001 is not in use by another application
- Try running `start-server.ps1` if the .bat file doesn't work

### Performance Issues

- Close other resource-intensive applications
- Ensure you have at least 4GB available RAM
- Check your internet connection for AI features

### Firewall/Antivirus

- The application may be flagged by some antivirus software
- Add an exception for `Friday-AI-Assistant-v1.20.2-FIXED.exe`
- Allow Node.js through the firewall when prompted
- The application uses local ports for internal communication

## 📞 Support

For support, issues, or feature requests:

- Check the application's built-in help system
- Review the troubleshooting section above
- Ensure you're running the latest version

## 📄 License

This software is distributed under the terms specified in the application.

## 🔄 Updates

To update to a newer version:

1. Download the latest executable
2. Close the current application
3. Replace the old executable with the new one
4. Restart the application

---

**Built with ❤️ using Tauri, Next.js, and modern web technologies**

_Version 1.20.1 - Built on 2025-08-10_
